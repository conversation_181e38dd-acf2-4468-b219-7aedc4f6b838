#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化版DXF解析器
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(__file__))

from data_process.cad.tmp.dxf_parser_optimized import DXFOptimizedParser, process_dxf_optimized


def test_single_file():
    """测试单个文件解析"""
    print("=== 测试单个文件解析 ===")
    
    # 这里需要替换为实际的DXF文件路径
    test_file = "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test/sample.dxf"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        print("请提供一个有效的DXF文件路径进行测试")
        return
    
    try:
        # 创建解析器（不包含坐标）
        parser = DXFOptimizedParser(test_file, include_coordinates=False)
        
        # 执行解析
        result = parser.generate_optimized_output()
        
        # 显示结果
        print(f"文件: {result['文件元数据']['文件名']}")
        print(f"DXF版本: {result['文件元数据']['DXF版本']}")
        print(f"总文本实体数: {result['文件元数据']['总文本实体数']}")
        print(f"图纸数量: {result['图纸结构']['图纸数量']}")
        
        print("\n内容分类:")
        content_class = result['内容分类']
        print(f"  主图文本: {content_class['主图文本数量']}个")
        print(f"  图例文本: {content_class['图例文本数量']}个")
        print(f"  表格文本: {content_class['表格文本数量']}个")
        
        print(f"\n检测到表格: {len(result['表格数据'])}个")
        for i, table in enumerate(result['表格数据']):
            print(f"  表格{i+1}: {table['表格描述']} ({table['行数']}行 x {table['列数']}列)")
        
        # 显示图例文本示例
        if content_class['图例文本']:
            print(f"\n图例文本示例:")
            for text in content_class['图例文本'][:5]:
                print(f"  - {text}")
        
        # 显示表格文本示例
        if content_class['表格文本']:
            print(f"\n表格文本示例:")
            for text in content_class['表格文本'][:5]:
                print(f"  - {text}")
        
        # 显示markdown表格
        if result['表格markdown输出'] != "未检测到表格结构":
            print(f"\n=== Markdown表格输出 ===")
            print(result['表格markdown输出'][:500] + "..." if len(result['表格markdown输出']) > 500 else result['表格markdown输出'])
        
        print("\n✅ 单文件测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_batch_processing():
    """测试批量处理"""
    print("\n=== 测试批量处理 ===")
    
    # 测试目录
    test_dir = "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test"
    output_dir = "/tmp/dxf_test_output"
    
    if not os.path.exists(test_dir):
        print(f"测试目录不存在: {test_dir}")
        print("请提供一个包含DXF文件的目录进行测试")
        return
    
    try:
        results = process_dxf_optimized(
            test_dir,
            output_dir,
            include_coordinates=False
        )
        
        print(f"\n批量处理结果:")
        print(f"  总文件数: {results['总计']}")
        print(f"  成功解析: {results['成功']}")
        print(f"  解析失败: {results['失败']}")
        
        if results['成功'] > 0:
            print(f"  成功率: {(results['成功'] / results['总计']) * 100:.1f}%")
            print(f"  输出目录: {output_dir}")
        
        print("\n✅ 批量处理测试完成")
        
    except Exception as e:
        print(f"❌ 批量处理测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_with_coordinates():
    """测试包含坐标的解析"""
    print("\n=== 测试包含坐标的解析 ===")
    
    test_file = "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test/sample.dxf"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    try:
        # 创建解析器（包含坐标）
        parser = DXFOptimizedParser(test_file, include_coordinates=True)
        
        # 执行解析
        result = parser.generate_optimized_output()
        
        print(f"包含坐标: {result['文件元数据']['包含坐标']}")
        print(f"检测到表格: {len(result['表格数据'])}个")
        
        # 显示表格结构化信息
        for i, table in enumerate(result['表格数据']):
            print(f"\n表格{i+1} - {table['表格描述']}:")
            print(f"  行数: {table['行数']}")
            print(f"  列数: {table['列数']}")
            print(f"  表格类型: {table['表格类型']}")
            
            # 显示前几行内容
            for j, row in enumerate(table['内容'][:3]):
                print(f"  第{j+1}行: {row['内容']}")
        
        print("\n✅ 坐标解析测试完成")
        
    except Exception as e:
        print(f"❌ 坐标解析测试失败: {e}")
        import traceback
        traceback.print_exc()


def display_classification_examples():
    """显示分类示例"""
    print("\n=== 内容分类示例 ===")
    
    from data_process.cad.tmp.dxf_parser_optimized import DXFOptimizedParser
    
    # 创建临时解析器实例来测试分类逻辑
    parser = DXFOptimizedParser("", include_coordinates=False)
    
    # 测试文本分类
    test_texts = [
        ("图例说明", "0"),
        ("符号标识", "LEGEND"),
        ("工程号: ABC123", "0"),
        ("设计: 张三", "TITLE"),
        ("审核: 李四", "0"),
        ("项目名称: 核电站建设", "0"),
        ("普通文本内容", "0"),
        ("LEGEND", "0"),
        ("PROJECT: Nuclear Plant", "0"),
        ("DRAWING NO: 001", "0")
    ]
    
    print("文本分类测试:")
    for text, layer in test_texts:
        classification = parser.classify_content_by_keywords(text, layer)
        print(f"  '{text}' (图层: {layer}) -> {classification}")
    
    print("\n✅ 分类示例完成")


def main():
    """主测试函数"""
    print("DXF优化解析器测试")
    print("=" * 50)
    
    # 显示分类示例
    display_classification_examples()
    
    # 测试单文件解析
    test_single_file()
    
    # 测试包含坐标的解析
    test_with_coordinates()
    
    # 测试批量处理
    test_batch_processing()
    
    print("\n" + "=" * 50)
    print("所有测试完成")


if __name__ == '__main__':
    main()
