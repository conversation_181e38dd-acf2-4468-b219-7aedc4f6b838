# DXF结构化解析器 - 优化版本

## 概述

这是一个专门针对DXF文件结构化解析进行优化的版本，主要解决以下问题：

1. **改进图例和表格识别**：基于关键字准确区分图例和工程号表格
2. **表格结构化输出**：将解析后的表格数据转换为markdown格式
3. **简化输出结构**：默认不输出坐标信息，减少输出复杂度
4. **多图纸支持**：支持一个文件包含多张图纸的情况

## 主要优化

### 1. 图例和表格识别逻辑

**图例关键字**（带有图例的是图例）：
- 中文：图例、说明、符号、标识、图标、标记、符号说明、图形符号、标准符号
- 英文：LEGEND、Legend、SYMBOL

**工程号/表格关键字**（带有工程号关键字的是表格）：
- 中文：工程号、项目号、图号、设计、审核、校对、批准、工程名称、项目名称、图纸名称、比例、日期、版本
- 英文：PROJECT、DRAWING、SCALE、DATE、VERSION、CHECKED、APPROVED、DESIGNED

### 2. 表格结构化输出

解析后的表格会被转换为标准的markdown表格格式：

```markdown
### 表格1 - 工程信息表

| 列1 | 列2 | 列3 |
| --- | --- | --- |
| 工程号 | ABC123 | |
| 设计 | 张三 | |
| 审核 | 李四 | |
```

### 3. 输出结构

```json
{
  "文件元数据": {
    "文件名": "example.dxf",
    "DXF版本": "AC1027",
    "总文本实体数": 1250,
    "包含坐标": false
  },
  "图纸结构": {
    "图纸数量": 2,
    "图纸列表": [...]
  },
  "内容分类": {
    "主图文本数量": 800,
    "图例文本数量": 50,
    "表格文本数量": 400,
    "主图文本": [...],
    "图例文本": [...],
    "表格文本": [...]
  },
  "表格数据": [...],
  "表格markdown输出": "..."
}
```

## 使用方法

### 1. 基本使用

```python
from dxf_parser_optimized import DXFOptimizedParser

# 创建解析器（默认不包含坐标）
parser = DXFOptimizedParser("example.dxf", include_coordinates=False)

# 执行解析
result = parser.generate_optimized_output()

# 查看结果
print(f"检测到 {len(result['表格数据'])} 个表格")
print(result['表格markdown输出'])
```

### 2. 批量处理

```python
from dxf_parser_optimized import process_dxf_optimized

# 批量处理目录中的所有DXF文件
results = process_dxf_optimized(
    input_path="/path/to/dxf/files",
    output_base="/path/to/output",
    include_coordinates=False
)

print(f"成功处理 {results['成功']} 个文件")
```

### 3. 命令行使用

```bash
# 处理单个文件
python dxf_parser_optimized.py /path/to/file.dxf /path/to/output

# 处理目录（包含坐标）
python dxf_parser_optimized.py /path/to/directory /path/to/output true
```

## 输出文件

对于每个DXF文件，会生成以下输出：

1. **JSON文件**：`filename_optimized.json` - 完整的结构化数据
2. **Markdown文件**：`filename_tables.md` - 表格的markdown格式（如果检测到表格）

## 测试

运行测试脚本验证功能：

```bash
python test_optimized_parser.py
```

测试包括：
- 单文件解析测试
- 批量处理测试
- 坐标解析测试
- 内容分类测试

## 配置选项

### include_coordinates

- `False`（默认）：不包含坐标信息，输出更简洁，处理更快
- `True`：包含坐标信息，支持表格结构检测和多图纸区域检测

### 建议配置

- **快速分类**：`include_coordinates=False` - 适用于只需要文本分类的场景
- **完整解析**：`include_coordinates=True` - 适用于需要表格结构和位置信息的场景

## 特性对比

| 特性 | 原版本 | 优化版本 |
|------|--------|----------|
| 图例识别 | 基于位置 | 基于关键字 |
| 表格识别 | 复杂逻辑 | 关键字+结构 |
| 输出格式 | JSON | JSON + Markdown |
| 坐标信息 | 默认包含 | 默认不包含 |
| 处理速度 | 较慢 | 更快 |
| 输出大小 | 较大 | 更小 |

## 注意事项

1. **关键字匹配**：确保DXF文件中的文本包含相应的关键字才能正确分类
2. **表格结构**：只有在包含坐标信息时才能检测表格的行列结构
3. **多图纸检测**：需要坐标信息来区分不同的图纸区域
4. **文本编码**：支持中文和英文混合的DXF文件

## 依赖项

```bash
pip install ezdxf tqdm
```

## 更新日志

### v1.0 (优化版本)
- 改进图例和表格识别逻辑
- 添加markdown表格输出
- 默认不输出坐标信息
- 优化处理速度和输出大小
- 添加完整的测试套件
