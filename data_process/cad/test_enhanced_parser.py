#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版DXF解析器
专门测试多分图纸和表格重建功能
"""

import os
import sys
import json
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def find_test_dxf_file():
    """查找可用的测试DXF文件"""
    # 可能的测试文件路径
    possible_paths = [
        "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test/sample.dxf",
        "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test",
        "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化",
        "./test_data",
        "."
    ]

    for base_path in possible_paths:
        if os.path.exists(base_path):
            if os.path.isfile(base_path) and base_path.endswith('.dxf'):
                return base_path
            elif os.path.isdir(base_path):
                # 在目录中查找DXF文件
                for root, dirs, files in os.walk(base_path):
                    for file in files:
                        if file.lower().endswith('.dxf'):
                            return os.path.join(root, file)

    return None

def test_enhanced_features():
    """测试增强功能"""
    print("🚀 测试增强版DXF解析器")
    print("=" * 60)

    # 查找测试文件
    test_file = find_test_dxf_file()

    if not test_file:
        print("❌ 未找到可用的DXF测试文件")
        print("请将DXF文件放在以下位置之一:")
        print("  - 当前目录")
        print("  - ./test_data/ 目录")
        print("  - 或修改脚本中的路径")
        return False

    print(f"✅ 找到测试文件: {test_file}")
    
    try:
        from tmp.dxf_parser_optimized_v4 import DXFStructureParser
        
        print(f"📁 测试文件: {os.path.basename(test_file)}")
        print("🔄 开始解析...")
        
        # 创建解析器
        parser = DXFStructureParser(test_file)
        result = parser.parse()
        
        if "错误" in result:
            print(f"❌ 解析失败: {result['错误']}")
            return False
        
        # 显示基本信息
        print("\n📊 解析结果:")
        print(f"   文件名: {result['文件元数据']['文件名']}")
        print(f"   DXF版本: {result['文件元数据']['DXF版本']}")
        print(f"   图纸数量: {result['文件元数据']['图纸数量']}")
        
        # 显示图纸信息
        print("\n📋 图纸详情:")
        for i, sheet_data in enumerate(result['图纸结构']):
            print(f"   图纸 {i+1}: {sheet_data['图纸名称']}")
            print(f"     - 表格数量: {len(sheet_data['表格列表'])}")
            print(f"     - 主图内容: {len(sheet_data['主图内容'])} 项")
            print(f"     - 图例内容: {len(sheet_data['图例内容'])} 项")
            
            # 显示表格详情
            for j, table in enumerate(sheet_data['表格列表']):
                print(f"     - 表格 {j+1}: {table['表格标题']} ({table['表格类型']})")
                print(f"       尺寸: {table['行数']}行 × {table['列数']}列")
                if table.get('合并单元格'):
                    print(f"       合并单元格: {len(table['合并单元格'])}个")
        
        # 保存测试结果
        output_file = './test_enhanced_result.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n✅ 测试完成，结果已保存到: {output_file}")
        
        # 测试表格导出功能
        print("\n🔄 测试表格导出功能...")
        test_table_export(parser, test_file)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_table_export(parser, dxf_file):
    """测试表格导出功能"""
    try:
        # 重新解析以获取sheets对象
        parser._extract_all_entities()
        sheets = parser._detect_sheets()
        
        if not sheets:
            print("   ⚠️  未检测到图纸，跳过表格导出测试")
            return
        
        # 重建表格
        for sheet in sheets:
            sheet.tables = parser._reconstruct_tables_in_sheet(sheet)
        
        # 导出表格
        output_dir = "./test_tables_export"
        table_count = parser.export_tables_to_files(sheets, output_dir)
        
        if table_count > 0:
            print(f"   ✅ 成功导出 {table_count} 个表格到 {output_dir}")
        else:
            print("   ⚠️  未找到可导出的表格")
            
    except Exception as e:
        print(f"   ❌ 表格导出测试失败: {e}")

def test_sheet_detection_strategies():
    """测试不同的图纸检测策略"""
    print("\n🔍 测试图纸检测策略")
    print("=" * 40)

    test_file = find_test_dxf_file()

    if not test_file:
        print("❌ 测试文件不存在，跳过策略测试")
        return
    
    try:
        from tmp.dxf_parser_optimized_v4 import DXFStructureParser
        
        parser = DXFStructureParser(test_file)
        parser._extract_all_entities()
        
        print(f"📁 测试文件: {os.path.basename(test_file)}")
        print(f"   实体统计: {len(parser.lines)} 条线, {len(parser.texts)} 个文本")
        
        # 测试策略1: 矩形检测
        sheets_rect = parser._detect_sheets_by_rectangles()
        print(f"   策略1 (矩形检测): 找到 {len(sheets_rect)} 个图纸")
        
        # 测试策略2: 空白区域分割
        sheets_blank = parser._detect_sheets_by_blank_areas()
        print(f"   策略2 (空白区域): 找到 {len(sheets_blank)} 个图纸")
        
        # 测试策略3: 空间聚类
        try:
            sheets_cluster = parser._detect_sheets_by_clustering()
            print(f"   策略3 (空间聚类): 找到 {len(sheets_cluster)} 个图纸")
        except Exception as e:
            print(f"   策略3 (空间聚类): 不可用 - {e}")
        
        # 显示最终选择的策略结果
        final_sheets = parser._detect_sheets()
        print(f"   最终结果: 选择了 {len(final_sheets)} 个图纸")
        
    except Exception as e:
        print(f"❌ 策略测试失败: {e}")

def test_dependencies():
    """测试依赖项"""
    print("\n🔧 检查依赖项")
    print("=" * 30)
    
    # 检查基础依赖
    try:
        import ezdxf
        print("✅ ezdxf - 已安装")
    except ImportError:
        print("❌ ezdxf - 未安装")
    
    try:
        import numpy
        print("✅ numpy - 已安装")
    except ImportError:
        print("❌ numpy - 未安装")
    
    # 检查可选依赖
    try:
        import pandas
        print("✅ pandas - 已安装 (表格导出功能可用)")
    except ImportError:
        print("⚠️  pandas - 未安装 (表格导出功能受限)")
    
    try:
        import sklearn
        print("✅ sklearn - 已安装 (空间聚类功能可用)")
    except ImportError:
        print("⚠️  sklearn - 未安装 (空间聚类功能受限)")
    
    try:
        import openpyxl
        print("✅ openpyxl - 已安装 (Excel导出功能可用)")
    except ImportError:
        print("⚠️  openpyxl - 未安装 (Excel导出功能受限)")

def main():
    """主测试函数"""
    print("🎯 增强版DXF解析器测试套件")
    print("=" * 80)
    
    # 1. 检查依赖项
    test_dependencies()
    
    # 2. 测试图纸检测策略
    test_sheet_detection_strategies()
    
    # 3. 测试主要功能
    success = test_enhanced_features()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 所有测试完成！增强版解析器功能正常。")
        print("\n📚 新增功能:")
        print("   ✅ 多种图纸检测策略 (矩形/空白区域/空间聚类)")
        print("   ✅ 增强的表格重建算法")
        print("   ✅ 合并单元格检测")
        print("   ✅ 表格导出功能 (Excel/CSV)")
        print("   ✅ 改进的网格线分析")
    else:
        print("❌ 测试过程中遇到问题，请检查错误信息。")
    
    print("\n💡 使用建议:")
    print("   - 安装可选依赖以获得完整功能: pip install pandas scikit-learn openpyxl")
    print("   - 对于复杂图纸，建议使用空间聚类策略")
    print("   - 表格导出功能支持Excel和CSV格式")

if __name__ == '__main__':
    main()
