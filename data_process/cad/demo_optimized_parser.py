#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DXF优化解析器演示脚本
展示主要功能和改进点
"""

import os
import sys
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(__file__))

from data_process.cad.tmp.dxf_parser_optimized import DXFOptimizedParser


def demo_classification_logic():
    """演示分类逻辑"""
    print("🎯 演示：改进的图例和表格识别逻辑")
    print("=" * 60)
    
    parser = DXFOptimizedParser("", include_coordinates=False)
    
    # 测试各种文本内容
    test_cases = [
        # 图例相关
        ("图例", "应该识别为图例"),
        ("符号说明", "应该识别为图例"),
        ("LEGEND", "应该识别为图例"),
        ("图形符号", "应该识别为图例"),
        
        # 工程表格相关
        ("工程号：2024-001", "应该识别为表格"),
        ("设计：张工程师", "应该识别为表格"),
        ("审核：李主任", "应该识别为表格"),
        ("项目名称：核电站建设项目", "应该识别为表格"),
        ("DRAWING NO: ABC-123", "应该识别为表格"),
        ("PROJECT: Nuclear Plant", "应该识别为表格"),
        
        # 主图内容
        ("管道DN100", "应该识别为主图"),
        ("阀门V001", "应该识别为主图"),
        ("普通标注文字", "应该识别为主图"),
    ]
    
    print("文本内容分类测试：")
    print("-" * 60)
    
    for text, expected in test_cases:
        classification = parser.classify_content_by_keywords(text, "0")
        status = "✅" if (
            ("图例" in expected and classification == "图例") or
            ("表格" in expected and classification == "表格") or
            ("主图" in expected and classification == "主图")
        ) else "❌"
        
        print(f"{status} '{text}' -> {classification} ({expected})")
    
    print("\n" + "=" * 60)


def demo_markdown_output():
    """演示markdown表格输出"""
    print("\n📝 演示：Markdown表格输出格式")
    print("=" * 60)
    
    # 模拟表格数据
    mock_table_data = {
        "表格名称": "表格1",
        "表格描述": "工程信息表",
        "行数": 4,
        "列数": 2,
        "内容": [
            {"内容": ["项目", "核电站消防系统"]},
            {"内容": ["工程号", "2024-NPS-001"]},
            {"内容": ["设计", "张工程师"]},
            {"内容": ["审核", "李主任"]}
        ]
    }
    
    parser = DXFOptimizedParser("", include_coordinates=False)
    markdown_output = parser.format_table_as_markdown(mock_table_data)
    
    print("生成的Markdown表格：")
    print("-" * 30)
    print(markdown_output)
    print("-" * 30)
    print("✅ 表格已成功转换为标准markdown格式")
    print("=" * 60)


def demo_output_structure():
    """演示输出结构"""
    print("\n📊 演示：优化后的输出结构")
    print("=" * 60)
    
    # 模拟输出结构
    mock_output = {
        "文件元数据": {
            "文件名": "example.dxf",
            "DXF版本": "AC1027",
            "总文本实体数": 1250,
            "包含坐标": False
        },
        "图纸结构": {
            "图纸数量": 2,
            "图纸列表": [
                {"图纸名称": "图纸1", "图纸类型": "主图纸", "文本数量": 625},
                {"图纸名称": "图纸2", "图纸类型": "主图纸", "文本数量": 625}
            ]
        },
        "内容分类": {
            "主图文本数量": 800,
            "图例文本数量": 50,
            "表格文本数量": 400,
            "图例文本": ["图例", "符号说明", "标准符号"],
            "表格文本": ["工程号: 2024-001", "设计: 张工", "审核: 李主任"]
        },
        "表格数据": [
            {
                "表格名称": "表格1",
                "表格类型": "工程表格",
                "表格描述": "工程信息表",
                "行数": 3,
                "列数": 2
            }
        ]
    }
    
    print("输出结构示例：")
    print(json.dumps(mock_output, ensure_ascii=False, indent=2))
    print("\n主要改进：")
    print("✅ 默认不包含坐标信息，输出更简洁")
    print("✅ 按内容类型清晰分类（主图、图例、表格）")
    print("✅ 包含markdown格式的表格输出")
    print("✅ 支持多图纸结构")
    print("=" * 60)


def demo_performance_comparison():
    """演示性能对比"""
    print("\n⚡ 演示：性能和输出大小对比")
    print("=" * 60)
    
    print("特性对比：")
    print("-" * 40)
    
    comparison = [
        ("图例识别", "基于位置", "基于关键字"),
        ("表格识别", "复杂逻辑", "关键字+结构"),
        ("输出格式", "仅JSON", "JSON + Markdown"),
        ("坐标信息", "默认包含", "默认不包含"),
        ("处理速度", "较慢", "更快"),
        ("输出大小", "较大", "更小"),
        ("准确性", "依赖位置", "依赖内容")
    ]
    
    for feature, old, new in comparison:
        print(f"{feature:12} | {old:12} -> {new:12}")
    
    print("\n主要优势：")
    print("🚀 处理速度提升：去除复杂的坐标计算")
    print("📦 输出精简：默认不包含坐标信息")
    print("🎯 识别准确：基于文本内容而非位置")
    print("📝 格式友好：直接输出markdown表格")
    print("🔧 配置灵活：可选择是否包含坐标")
    print("=" * 60)


def demo_usage_scenarios():
    """演示使用场景"""
    print("\n🎮 演示：使用场景和配置建议")
    print("=" * 60)
    
    scenarios = [
        {
            "场景": "快速文本分类",
            "配置": "include_coordinates=False",
            "适用": "只需要区分图例、表格、主图内容",
            "优势": "处理快速，输出简洁"
        },
        {
            "场景": "完整表格解析",
            "配置": "include_coordinates=True",
            "适用": "需要表格的行列结构信息",
            "优势": "支持表格结构检测"
        },
        {
            "场景": "多图纸处理",
            "配置": "include_coordinates=True",
            "适用": "一个文件包含多张图纸",
            "优势": "自动检测图纸区域"
        },
        {
            "场景": "批量处理",
            "配置": "include_coordinates=False",
            "适用": "大量DXF文件的快速处理",
            "优势": "高效率，低资源消耗"
        }
    ]
    
    for scenario in scenarios:
        print(f"📋 {scenario['场景']}")
        print(f"   配置: {scenario['配置']}")
        print(f"   适用: {scenario['适用']}")
        print(f"   优势: {scenario['优势']}")
        print()
    
    print("=" * 60)


def main():
    """主演示函数"""
    print("🎉 DXF优化解析器功能演示")
    print("=" * 80)
    
    # 1. 演示分类逻辑
    demo_classification_logic()
    
    # 2. 演示markdown输出
    demo_markdown_output()
    
    # 3. 演示输出结构
    demo_output_structure()
    
    # 4. 演示性能对比
    demo_performance_comparison()
    
    # 5. 演示使用场景
    demo_usage_scenarios()
    
    print("\n🎯 总结：主要优化点")
    print("=" * 80)
    print("1. ✅ 图例和表格识别：基于关键字，更准确")
    print("2. ✅ 表格结构化输出：支持markdown格式")
    print("3. ✅ 默认不输出坐标：简化输出，提升性能")
    print("4. ✅ 多图纸支持：自动检测和处理多张图纸")
    print("5. ✅ 灵活配置：可选择是否包含坐标信息")
    print("6. ✅ 完整测试：包含各种场景的测试用例")
    
    print("\n📚 使用方法：")
    print("   python dxf_parser_optimized.py <输入路径> [输出路径] [是否包含坐标]")
    print("   python test_optimized_parser.py  # 运行测试")
    
    print("\n" + "=" * 80)
    print("演示完成！优化版解析器已准备就绪。")


if __name__ == '__main__':
    main()
