import ezdxf
import json
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
from typing import Dict, List, Any, Optional, Tuple, Union
from pathlib import Path


class DXFCoordinateAnalyzer:
    """
    DXF坐标分析器 - 专门用于分析和可视化坐标分布
    """

    def __init__(self, dxf_path: str):
        self.dxf_path = dxf_path
        self.doc = None
        self.all_coordinates = []
        self.text_entities = []

    def load_and_analyze(self):
        """加载文档并分析坐标分布"""
        try:
            self.doc = ezdxf.readfile(self.dxf_path)
            print(f"✓ 成功加载DXF文件: {self.dxf_path}")
        except Exception as e:
            print(f"✗ 加载失败: {e}")
            return False

        self._extract_all_coordinates()
        self._analyze_coordinate_distribution()
        return True

    def _extract_all_coordinates(self):
        """提取所有坐标"""
        self.all_coordinates = []
        self.text_entities = []

        # 处理所有空间
        spaces = [("模型空间", self.doc.modelspace())]
        for layout in self.doc.layouts:
            if layout.name != 'Model':
                spaces.append((f"图纸空间-{layout.name}", layout))

        for space_name, space in spaces:
            for entity in space:
                self._process_entity(entity, space_name)

        print(f"提取完成: 总坐标点数={len(self.all_coordinates)}, 文本实体数={len(self.text_entities)}")

    def _process_entity(self, entity, space_name: str):
        """处理单个实体"""
        entity_type = entity.dxftype()

        try:
            coords = None
            text_content = ""

            if entity_type in ['TEXT', 'MTEXT']:
                coords = self._safe_get_coords(entity.dxf.insert)
                if entity_type == 'TEXT':
                    text_content = getattr(entity.dxf, 'text', '')
                else:  # MTEXT
                    text_content = entity.plain_text()

            elif entity_type in ['ATTRIB', 'ATTDEF']:
                coords = self._safe_get_coords(entity.dxf.insert)
                text_content = getattr(entity.dxf, 'text', '')

            elif entity_type == 'INSERT':
                coords = self._safe_get_coords(entity.dxf.insert)
                # 提取块属性
                attr_texts = []
                if hasattr(entity, 'attribs'):
                    for attrib in entity.attribs:
                        if hasattr(attrib.dxf, 'text'):
                            attr_texts.append(attrib.dxf.text)
                text_content = "; ".join(attr_texts) if attr_texts else ""

            elif entity_type == 'LINE':
                start_coords = self._safe_get_coords(entity.dxf.start)
                end_coords = self._safe_get_coords(entity.dxf.end)
                if start_coords:
                    self.all_coordinates.append({
                        "坐标": start_coords,
                        "类型": "线-起点",
                        "实体类型": entity_type,
                        "图层": getattr(entity.dxf, 'layer', ''),
                        "空间": space_name
                    })
                if end_coords:
                    self.all_coordinates.append({
                        "坐标": end_coords,
                        "类型": "线-终点",
                        "实体类型": entity_type,
                        "图层": getattr(entity.dxf, 'layer', ''),
                        "空间": space_name
                    })
                return

            elif entity_type in ['CIRCLE', 'ARC']:
                coords = self._safe_get_coords(entity.dxf.center)

            elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                # 多段线的顶点
                try:
                    for i, vertex in enumerate(entity):
                        if hasattr(vertex, 'dxf'):
                            vertex_coords = self._safe_get_coords(vertex.dxf.location)
                        else:
                            vertex_coords = self._safe_get_coords(vertex)

                        if vertex_coords:
                            self.all_coordinates.append({
                                "坐标": vertex_coords,
                                "类型": f"多段线顶点-{i}",
                                "实体类型": entity_type,
                                "图层": getattr(entity.dxf, 'layer', ''),
                                "空间": space_name
                            })
                except:
                    pass
                return

            # 添加坐标到列表
            if coords:
                coord_info = {
                    "坐标": coords,
                    "类型": "插入点" if entity_type in ['TEXT', 'MTEXT', 'INSERT', 'ATTRIB', 'ATTDEF'] else "中心点",
                    "实体类型": entity_type,
                    "图层": getattr(entity.dxf, 'layer', ''),
                    "空间": space_name
                }
                x,y ,d1,d2 = coords
                if text_content and y<0:
                    coord_info["文本内容"] = self._clean_text(text_content)
                    self.text_entities.append(coord_info)

                self.all_coordinates.append(coord_info)

        except Exception as e:
            print(f"处理实体时出错 {entity_type}: {e}")

    def _safe_get_coords(self, coord_obj) -> Optional[List[float]]:
        """安全获取坐标"""
        if coord_obj is None:
            return None

        try:
            if isinstance(coord_obj, (list, tuple)):
                return [float(x) for x in coord_obj]
            elif hasattr(coord_obj, 'x') and hasattr(coord_obj, 'y'):
                z = getattr(coord_obj, 'z', 0.0)
                return [float(coord_obj.x), float(coord_obj.y), float(z)]
            elif hasattr(coord_obj, '__iter__'):
                return [float(x) for x in coord_obj]
            else:
                return [float(coord_obj)]
        except:
            return None

    def _clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        import re
        text = re.sub(r'\\[A-Za-z][0-9]*;?', '', text)
        text = re.sub(r'\\[{}]', '', text)
        text = re.sub(r'\\P', '\n', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()

    def _analyze_coordinate_distribution(self):
        """分析坐标分布"""
        if not self.all_coordinates:
            print("没有找到坐标数据")
            return

        # 提取X, Y坐标
        x_coords = [coord["坐标"][0] for coord in self.all_coordinates if len(coord["坐标"]) >= 2]
        y_coords = [coord["坐标"][1] for coord in self.all_coordinates if len(coord["坐标"]) >= 2]

        if not x_coords or not y_coords:
            print("坐标数据不足")
            return

        print(f"\n=== 坐标分布分析 ===")
        print(f"X坐标范围: {min(x_coords):.2f} ~ {max(x_coords):.2f}")
        print(f"Y坐标范围: {min(y_coords):.2f} ~ {max(y_coords):.2f}")
        print(f"X跨度: {max(x_coords) - min(x_coords):.2f}")
        print(f"Y跨度: {max(y_coords) - min(y_coords):.2f}")

        # 分析文本坐标分布
        if self.text_entities:
            text_x = [coord["坐标"][0] for coord in self.text_entities]
            text_y = [coord["坐标"][1] for coord in self.text_entities]

            print(f"\n=== 文本坐标分布 ===")
            print(f"文本X坐标范围: {min(text_x):.2f} ~ {max(text_x):.2f}")
            print(f"文本Y坐标范围: {min(text_y):.2f} ~ {max(text_y):.2f}")
            print(f"文本X跨度: {max(text_x) - min(text_x):.2f}")
            print(f"文本Y跨度: {max(text_y) - min(text_y):.2f}")

    def visualize_coordinates(self, save_path: Optional[str] = None):
        """可视化坐标分布"""
        if not self.all_coordinates:
            print("没有坐标数据可以可视化")
            return

        # 准备数据
        x_coords = [coord["坐标"][0] for coord in self.all_coordinates if len(coord["坐标"]) >= 2]
        y_coords = [coord["坐标"][1] for coord in self.all_coordinates if len(coord["坐标"]) >= 2]

        # 分别处理文本和非文本实体
        text_x = [coord["坐标"][0] for coord in self.text_entities]
        text_y = [coord["坐标"][1] for coord in self.text_entities]

        other_x = [coord["坐标"][0] for coord in self.all_coordinates
                   if len(coord["坐标"]) >= 2 and coord not in self.text_entities]
        other_y = [coord["坐标"][1] for coord in self.all_coordinates
                   if len(coord["坐标"]) >= 2 and coord not in self.text_entities]

        # 创建图形
        plt.figure(figsize=(15, 10))

        # 绘制所有坐标点
        if other_x and other_y:
            plt.scatter(other_x, other_y, c='lightblue', alpha=0.5, s=1, label='其他实体')

        if text_x and text_y:
            plt.scatter(text_x, text_y, c='red', alpha=0.7, s=3, label='文本实体')

        plt.xlabel('X坐标')
        plt.ylabel('Y坐标')
        plt.title(f'DXF坐标分布图 - {Path(self.dxf_path).name}')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 设置坐标轴比例相等
        plt.axis('equal')

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"坐标分布图已保存到: {save_path}")

        plt.show()

    def detect_sheets_by_advanced_clustering(self) -> List[Dict]:
        """改进的图纸检测算法"""
        if not self.text_entities:
            return []

        # 只使用文本实体进行聚类
        text_coords = np.array([[coord["坐标"][0], coord["坐标"][1]] for coord in self.text_entities])

        print(f"\n=== 高级图纸检测 ===")
        print(f"文本实体数量: {len(text_coords)}")

        # 方法1: X坐标密度分析
        sheets_by_density = self._detect_by_density_analysis(text_coords)

        # 方法2: 聚类分析
        sheets_by_clustering = self._detect_by_clustering(text_coords)

        # 方法3: 间隙分析
        sheets_by_gaps = self._detect_by_gap_analysis(text_coords)

        print(f"密度分析检测到: {len(sheets_by_density)} 个图纸")
        print(f"聚类分析检测到: {len(sheets_by_clustering)} 个图纸")
        print(f"间隙分析检测到: {len(sheets_by_gaps)} 个图纸")

        # 选择最佳结果
        if len(sheets_by_gaps) > 1:
            return sheets_by_gaps
        elif len(sheets_by_clustering) > 1:
            return sheets_by_clustering
        elif len(sheets_by_density) > 1:
            return sheets_by_density
        else:
            # 单图纸
            return [{
                "图纸名称": "图纸1",
                "图纸类型": "单页图纸",
                "X范围": [float(text_coords[:, 0].min()), float(text_coords[:, 0].max())],
                "Y范围": [float(text_coords[:, 1].min()), float(text_coords[:, 1].max())],
                "文本数量": len(text_coords),
                "检测方法": "默认单图纸"
            }]

    def _detect_by_density_analysis(self, coords: np.ndarray) -> List[Dict]:
        """基于密度分析的图纸检测"""
        x_coords = coords[:, 0]

        # 创建更细致的直方图
        num_bins = max(100, len(coords) // 10)
        hist, bin_edges = np.histogram(x_coords, bins=num_bins)

        # 计算移动平均以平滑数据
        window_size = max(3, num_bins // 20)
        smoothed_hist = np.convolve(hist, np.ones(window_size) / window_size, mode='same')

        # 寻找局部最小值（可能的分割点）
        from scipy.signal import find_peaks

        # 反转数据寻找谷底
        inverted = -smoothed_hist
        peaks, _ = find_peaks(inverted, height=-np.max(smoothed_hist) * 0.1)

        if len(peaks) > 0:
            # 选择最深的谷底作为分割点
            deepest_valley_idx = peaks[np.argmax(inverted[peaks])]
            split_x = bin_edges[deepest_valley_idx]

            left_coords = coords[coords[:, 0] < split_x]
            right_coords = coords[coords[:, 0] >= split_x]

            if len(left_coords) > 10 and len(right_coords) > 10:
                return [
                    {
                        "图纸名称": "图纸1",
                        "图纸类型": "左侧图纸",
                        "X范围": [float(left_coords[:, 0].min()), float(left_coords[:, 0].max())],
                        "Y范围": [float(left_coords[:, 1].min()), float(left_coords[:, 1].max())],
                        "文本数量": len(left_coords),
                        "检测方法": "密度分析"
                    },
                    {
                        "图纸名称": "图纸2",
                        "图纸类型": "右侧图纸",
                        "X范围": [float(right_coords[:, 0].min()), float(right_coords[:, 0].max())],
                        "Y范围": [float(right_coords[:, 1].min()), float(right_coords[:, 1].max())],
                        "文本数量": len(right_coords),
                        "检测方法": "密度分析"
                    }
                ]

        return []

    def _detect_by_clustering(self, coords: np.ndarray) -> List[Dict]:
        """基于聚类的图纸检测"""
        try:
            from sklearn.cluster import KMeans, DBSCAN
            from sklearn.preprocessing import StandardScaler

            # 标准化坐标
            scaler = StandardScaler()
            scaled_coords = scaler.fit_transform(coords)

            # 尝试DBSCAN聚类
            dbscan = DBSCAN(eps=0.3, min_samples=max(10, len(coords) // 20))
            cluster_labels = dbscan.fit_predict(scaled_coords)

            unique_labels = set(cluster_labels)
            if -1 in unique_labels:
                unique_labels.remove(-1)  # 移除噪声点

            if len(unique_labels) >= 2:
                sheets = []
                for i, label in enumerate(sorted(unique_labels)):
                    cluster_coords = coords[cluster_labels == label]
                    if len(cluster_coords) > 10:
                        sheets.append({
                            "图纸名称": f"图纸{i + 1}",
                            "图纸类型": f"聚类{label}",
                            "X范围": [float(cluster_coords[:, 0].min()), float(cluster_coords[:, 0].max())],
                            "Y范围": [float(cluster_coords[:, 1].min()), float(cluster_coords[:, 1].max())],
                            "文本数量": len(cluster_coords),
                            "检测方法": "DBSCAN聚类"
                        })
                return sheets

            # 回退到K-means
            if len(coords) > 50:
                kmeans = KMeans(n_clusters=2, random_state=42, n_init=10)
                cluster_labels = kmeans.fit_predict(coords)

                cluster_0 = coords[cluster_labels == 0]
                cluster_1 = coords[cluster_labels == 1]

                if len(cluster_0) > 10 and len(cluster_1) > 10:
                    # 按X坐标中心排序
                    center_0_x = np.mean(cluster_0[:, 0])
                    center_1_x = np.mean(cluster_1[:, 0])

                    if center_0_x < center_1_x:
                        left_coords, right_coords = cluster_0, cluster_1
                    else:
                        left_coords, right_coords = cluster_1, cluster_0

                    return [
                        {
                            "图纸名称": "图纸1",
                            "图纸类型": "左侧图纸",
                            "X范围": [float(left_coords[:, 0].min()), float(left_coords[:, 0].max())],
                            "Y范围": [float(left_coords[:, 1].min()), float(left_coords[:, 1].max())],
                            "文本数量": len(left_coords),
                            "检测方法": "K-means聚类"
                        },
                        {
                            "图纸名称": "图纸2",
                            "图纸类型": "右侧图纸",
                            "X范围": [float(right_coords[:, 0].min()), float(right_coords[:, 0].max())],
                            "Y范围": [float(right_coords[:, 1].min()), float(right_coords[:, 1].max())],
                            "文本数量": len(right_coords),
                            "检测方法": "K-means聚类"
                        }
                    ]

        except ImportError:
            print("sklearn未安装，跳过聚类分析")
        except Exception as e:
            print(f"聚类分析出错: {e}")

        return []

    def _detect_by_gap_analysis(self, coords: np.ndarray) -> List[Dict]:
        """基于间隙分析的图纸检测"""
        x_coords = coords[:, 0]
        x_range = x_coords.max() - x_coords.min()

        # 更精细的间隙检测
        sorted_x = np.sort(x_coords)
        gaps = np.diff(sorted_x)

        # 寻找异常大的间隙
        gap_threshold = np.percentile(gaps, 95)  # 95百分位数
        large_gaps = gaps > gap_threshold

        if np.any(large_gaps):
            # 找到最大间隙的位置
            max_gap_idx = np.argmax(gaps)
            split_x = (sorted_x[max_gap_idx] + sorted_x[max_gap_idx + 1]) / 2

            # 检查间隙是否足够大
            if gaps[max_gap_idx] > x_range * 0.05:  # 间隙大于总范围的5%
                left_coords = coords[coords[:, 0] < split_x]
                right_coords = coords[coords[:, 0] >= split_x]

                if len(left_coords) > 10 and len(right_coords) > 10:
                    return [
                        {
                            "图纸名称": "图纸1",
                            "图纸类型": "左侧图纸",
                            "X范围": [float(left_coords[:, 0].min()), float(left_coords[:, 0].max())],
                            "Y范围": [float(left_coords[:, 1].min()), float(left_coords[:, 1].max())],
                            "文本数量": len(left_coords),
                            "检测方法": "间隙分析",
                            "分割点": float(split_x),
                            "间隙大小": float(gaps[max_gap_idx])
                        },
                        {
                            "图纸名称": "图纸2",
                            "图纸类型": "右侧图纸",
                            "X范围": [float(right_coords[:, 0].min()), float(right_coords[:, 0].max())],
                            "Y范围": [float(right_coords[:, 1].min()), float(right_coords[:, 1].max())],
                            "文本数量": len(right_coords),
                            "检测方法": "间隙分析",
                            "分割点": float(split_x),
                            "间隙大小": float(gaps[max_gap_idx])
                        }
                    ]

        return []

    def generate_detailed_report(self) -> Dict:
        """生成详细的分析报告"""
        sheets = self.detect_sheets_by_advanced_clustering()

        report = {
            "文件信息": {
                "文件名": Path(self.dxf_path).name,
                "文件路径": self.dxf_path
            },
            "坐标统计": {
                "总坐标点数": len(self.all_coordinates),
                "文本实体数": len(self.text_entities)
            },
            "图纸检测结果": {
                "检测到的图纸数": len(sheets),
                "图纸详情": sheets
            }
        }

        if self.all_coordinates:
            x_coords = [coord["坐标"][0] for coord in self.all_coordinates if len(coord["坐标"]) >= 2]
            y_coords = [coord["坐标"][1] for coord in self.all_coordinates if len(coord["坐标"]) >= 2]

            report["坐标统计"].update({
                "X坐标范围": [float(min(x_coords)), float(max(x_coords))],
                "Y坐标范围": [float(min(y_coords)), float(max(y_coords))],
                "X跨度": float(max(x_coords) - min(x_coords)),
                "Y跨度": float(max(y_coords) - min(y_coords))
            })

        return report


def analyze_dxf_coordinates(dxf_path: str, output_dir: Optional[str] = None):
    """分析DXF文件的坐标分布"""
    analyzer = DXFCoordinateAnalyzer(dxf_path)

    if not analyzer.load_and_analyze():
        return None

    # 生成报告
    report = analyzer.generate_detailed_report()

    # 创建输出目录
    if output_dir is None:
        output_dir = Path(dxf_path).parent / "coordinate_analysis"
    else:
        output_dir = Path(output_dir)

    output_dir.mkdir(exist_ok=True)

    # 保存报告
    report_path = output_dir / f"{Path(dxf_path).stem}_coordinate_analysis.json"
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    print(f"分析报告已保存到: {report_path}")

    # 生成可视化图
    viz_path = output_dir / f"{Path(dxf_path).stem}_coordinate_plot.png"
    analyzer.visualize_coordinates(str(viz_path))

    return report


def main():
    """主函数 - 用于测试坐标分析"""
    import sys

    if len(sys.argv) > 1:
        dxf_path = sys.argv[1]
        output_dir = sys.argv[2] if len(sys.argv) > 2 else None
    else:
        # 默认测试路径
        dxf_path = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10.dxf'
        output_dir = None

    try:
        report = analyze_dxf_coordinates(dxf_path, output_dir)

        if report:
            print("\n=== 分析结果摘要 ===")
            print(f"文件: {report['文件信息']['文件名']}")
            print(f"总坐标点数: {report['坐标统计']['总坐标点数']}")
            print(f"文本实体数: {report['坐标统计']['文本实体数']}")
            print(f"检测到图纸数: {report['图纸检测结果']['检测到的图纸数']}")

            for sheet in report['图纸检测结果']['图纸详情']:
                print(
                    f"  {sheet['图纸名称']}: X范围={sheet['X范围']}, Y范围={sheet['Y范围']}, 文本数={sheet['文本数量']}")

    except Exception as e:
        print(f"分析过程中出错: {e}")
        return 1

    return 0


if __name__ == '__main__':
    exit(main())
