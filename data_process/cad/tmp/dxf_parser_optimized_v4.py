import ezdxf
import json
import re
import os
import math
import numpy as np
from pathlib import Path
from collections import defaultdict
from dataclasses import dataclass, field
from typing import List, Dict, Any, Tuple, Optional
from ezdxf.math import BoundingBox, Vec3
from tqdm import tqdm

# 尝试导入可选依赖
try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
    print("警告: pandas未安装，表格导出功能将受限")

try:
    from sklearn.cluster import DBSCAN
    HAS_SKLEARN = True
except ImportError:
    HAS_SKLEARN = False
    print("警告: sklearn未安装，空间聚类功能将受限")

# --- 配置常量 ---
# 用于判断线条是否水平/垂直的容差 (单位：图形单位)
GEOMETRY_TOLERANCE = 1.0
# 用于判断是否为同一个表格的行/列坐标的合并容差
TABLE_GRID_TOLERANCE = 5.0
# 图纸外框的最小尺寸，小于此尺寸的矩形不会被当做图纸外框
MIN_SHEET_DIMENSION = 1000.0
# 图纸分割的最小空白区域宽度
MIN_BLANK_WIDTH = 200.0
# 空间聚类的最小距离参数
CLUSTERING_EPS = 100.0
# 空间聚类的最小样本数
CLUSTERING_MIN_SAMPLES = 5

# --- 关键词常量 ---
KEYWORD_LEGEND = ["图例", "说明", "符号", "标识", "图标", "标记", "图示"]
KEYWORD_TABLE = ["工程号", "项目号", "图号", "设计", "审核", "校对", "批准", "比例", "日期", "版本", "清单", "材料",
                 "设备", "参数"]


@dataclass
class TextEntity:
    """结构化的文本实体"""
    content: str
    pos: Vec3
    height: float = 0.0
    layer: str = "0"


@dataclass
class Table:
    """结构化的表格对象"""
    bbox: BoundingBox
    rows: int = 0
    cols: int = 0
    data: List[List[TextEntity]] = field(default_factory=list)
    title: str = "未命名表格"
    category: str = "数据表格"  # '数据表格', '图例表格', '工程表格'
    merged_cells: List[Tuple[int, int, int, int]] = field(default_factory=list)  # (start_row, start_col, end_row, end_col)
    x_grid: List[float] = field(default_factory=list)  # 列边界坐标
    y_grid: List[float] = field(default_factory=list)  # 行边界坐标

    def get_full_text(self) -> str:
        """获取表格内所有文本内容的拼接"""
        return " ".join(cell.content for row in self.data for cell in row if cell)

    def to_dataframe(self) -> Optional['pd.DataFrame']:
        """转换为pandas DataFrame"""
        if not HAS_PANDAS:
            return None

        # 创建二维数据数组
        data_array = []
        for row in self.data:
            row_data = [cell.content if cell else "" for cell in row]
            data_array.append(row_data)

        # 创建DataFrame
        df = pd.DataFrame(data_array)
        return df

    def export_to_excel(self, filepath: str) -> bool:
        """导出表格到Excel文件"""
        try:
            df = self.to_dataframe()
            if df is not None:
                df.to_excel(filepath, index=False, header=False)
                return True
        except Exception as e:
            print(f"导出Excel失败: {e}")
        return False

    def export_to_csv(self, filepath: str) -> bool:
        """导出表格到CSV文件"""
        try:
            df = self.to_dataframe()
            if df is not None:
                df.to_csv(filepath, index=False, header=False, encoding='utf-8-sig')
                return True
        except Exception as e:
            print(f"导出CSV失败: {e}")
        return False


@dataclass
class Sheet:
    """结构化的图纸对象"""
    name: str
    bbox: BoundingBox
    tables: List[Table] = field(default_factory=list)
    main_drawing_entities: List[Dict] = field(default_factory=list)
    legend_entities: List[Dict] = field(default_factory=list)
    unassigned_entities: List[Dict] = field(default_factory=list)


class DXFStructureParser:
    """
    DXF图纸结构化解析器 V4.1 - 几何优先版

    核心逻辑:
    1. 基于大型矩形框识别图纸 (Sheet)。
    2. 在图纸内识别由横平竖直线构成的网格，重建为表格 (Table)。
    3. 基于关键词对表格进行分类。
    4. 将其他实体分配到主图或图例区域。
    """

    def __init__(self, dxf_path: str):
        self.dxf_path = dxf_path
        self.doc = None
        self.msp = None
        self.all_entities = []

    def parse(self) -> Dict[str, Any]:
        """执行完整的解析流程"""
        try:
            self.doc = ezdxf.readfile(self.dxf_path)
            self.msp = self.doc.modelspace()
        except Exception as e:
            return {"错误": f"无法读取DXF文件: {e}"}

        # 1. 提取所有需要的实体
        self._extract_all_entities()

        # 2. 检测图纸区域
        sheets = self._detect_sheets()
        if not sheets:
            # 如果没有找到明确的图框，则将整个绘图区域视为一张图纸
            try:
                overall_bbox = self.msp.graphics_extents()
                if overall_bbox.has_data:
                    sheets.append(Sheet(name="主图纸", bbox=overall_bbox))
            except Exception:
                # 如果模型空间为空或无法计算边界，则返回错误
                pass

        if not sheets:
            return {"错误": "无法确定图纸边界，且图纸为空。"}

        # 3. 在每张图纸内重建表格并分配实体
        for sheet in sheets:
            # a. 重建表格
            sheet.tables = self._reconstruct_tables_in_sheet(sheet)
            # b. 分配其他实体
            self._assign_entities_to_sheet(sheet)

        # 4. 生成最终的结构化输出
        return self._generate_final_output(sheets)

    def _extract_all_entities(self):
        """从模型空间提取并初步分类实体"""
        self.lines = []
        self.polylines = []
        self.texts = []

        for entity in self.msp:
            etype = entity.dxftype()
            if etype == 'LINE':
                self.lines.append(entity)
            elif etype == 'LWPOLYLINE':
                self.polylines.append(entity)
            elif etype in ('TEXT', 'MTEXT'):
                self._extract_text(entity)

    def _extract_text(self, entity):
        """提取并清洗文本"""
        try:
            if entity.dxftype() == 'MTEXT':
                # 移除MTEXT格式化代码
                text_content = re.sub(r'\\[A-Za-z][0-9]*;?|\\{|\\}|\\P', ' ', entity.text).strip()
                pos = entity.dxf.insert
                height = entity.dxf.char_height
            else:  # TEXT
                text_content = entity.dxf.text.strip()
                pos = entity.dxf.insert
                height = entity.dxf.height

            if text_content:
                self.texts.append(TextEntity(
                    content=text_content,
                    pos=pos,
                    height=height,
                    layer=entity.dxf.layer
                ))
        except Exception:
            # 忽略无法解析的文本实体
            pass

    def _detect_sheets(self) -> List[Sheet]:
        """通过多种策略检测图纸区域"""
        sheets = []

        # 策略1: 检测大型矩形图框
        sheets.extend(self._detect_sheets_by_rectangles())

        # 策略2: 如果没有找到矩形图框，尝试基于X坐标空白区域分割
        if not sheets:
            sheets.extend(self._detect_sheets_by_blank_areas())

        # 策略3: 如果仍然没有找到，尝试空间聚类分析
        if not sheets and HAS_SKLEARN:
            sheets.extend(self._detect_sheets_by_clustering())

        # 如果所有策略都失败，将整个绘图区域视为一张图纸
        if not sheets:
            try:
                overall_bbox = self.msp.graphics_extents()
                if overall_bbox.has_data:
                    sheets.append(Sheet(name="主图纸", bbox=overall_bbox))
            except Exception:
                pass

        # 按X坐标排序图纸并命名
        sheets.sort(key=lambda s: s.bbox.extmin.x)
        for i, sheet in enumerate(sheets):
            if not sheet.name:
                sheet.name = f"图纸{i + 1}"

        if len(sheets) == 1:
            sheets[0].name = "主图纸"

        return sheets

    def _detect_sheets_by_rectangles(self) -> List[Sheet]:
        """通过检测大型矩形图框来识别图纸"""
        sheets = []
        for pline in self.polylines:
            if pline.is_closed:
                try:
                    # 使用上下文管理器获取点列表
                    with pline.points("xy") as points:
                        # 检查是否为矩形
                        if len(points) == 4 or (len(points) == 5 and len(points) > 0 and
                                               abs(points[0][0] - points[-1][0]) < GEOMETRY_TOLERANCE and
                                               abs(points[0][1] - points[-1][1]) < GEOMETRY_TOLERANCE):
                            # 将点转换为Vec3对象以创建包围盒
                            vec3_points = [Vec3(p[0], p[1], 0) for p in points]
                            bbox = BoundingBox(vec3_points)
                            # 检查尺寸是否满足最小图纸要求
                            if bbox.size.x > MIN_SHEET_DIMENSION and bbox.size.y > MIN_SHEET_DIMENSION:
                                sheets.append(Sheet(name="", bbox=bbox))
                except Exception:
                    continue
        return sheets

    def _detect_sheets_by_blank_areas(self) -> List[Sheet]:
        """基于X坐标空白区域分割图纸"""
        sheets = []

        # 获取所有实体的X坐标范围
        all_x_coords = []
        for line in self.lines:
            all_x_coords.extend([line.dxf.start.x, line.dxf.end.x])
        for text in self.texts:
            all_x_coords.append(text.pos.x)

        if not all_x_coords:
            return sheets

        # 排序并找到空白区域
        all_x_coords.sort()
        min_x, max_x = min(all_x_coords), max(all_x_coords)

        # 创建X坐标的直方图来找到空白区域
        x_range = max_x - min_x
        if x_range <= 0:
            return sheets

        # 将X轴分成若干个区间
        num_bins = max(10, int(x_range / 100))  # 每100单位一个区间
        bin_width = x_range / num_bins
        bins = [0] * num_bins

        # 统计每个区间内的实体数量
        for x in all_x_coords:
            bin_idx = min(int((x - min_x) / bin_width), num_bins - 1)
            bins[bin_idx] += 1

        # 找到连续的空白区域（实体数量很少的区间）
        threshold = max(1, len(all_x_coords) // (num_bins * 10))  # 动态阈值
        blank_regions = []
        in_blank = False
        blank_start = 0

        for i, count in enumerate(bins):
            if count <= threshold:
                if not in_blank:
                    blank_start = i
                    in_blank = True
            else:
                if in_blank:
                    blank_width = (i - blank_start) * bin_width
                    if blank_width >= MIN_BLANK_WIDTH:
                        blank_x_start = min_x + blank_start * bin_width
                        blank_x_end = min_x + i * bin_width
                        blank_regions.append((blank_x_start, blank_x_end))
                    in_blank = False

        # 根据空白区域分割图纸
        if blank_regions:
            # 创建图纸边界
            sheet_boundaries = [min_x]
            for blank_start, blank_end in blank_regions:
                sheet_boundaries.extend([blank_start, blank_end])
            sheet_boundaries.append(max_x)

            # 创建图纸对象
            for i in range(0, len(sheet_boundaries) - 1, 2):
                if i + 1 < len(sheet_boundaries):
                    x_min = sheet_boundaries[i]
                    x_max = sheet_boundaries[i + 1]

                    # 计算Y坐标范围
                    y_coords = []
                    for line in self.lines:
                        if x_min <= line.dxf.start.x <= x_max or x_min <= line.dxf.end.x <= x_max:
                            y_coords.extend([line.dxf.start.y, line.dxf.end.y])
                    for text in self.texts:
                        if x_min <= text.pos.x <= x_max:
                            y_coords.append(text.pos.y)

                    if y_coords:
                        y_min, y_max = min(y_coords), max(y_coords)
                        # 创建包围盒
                        corners = [
                            Vec3(x_min, y_min, 0),
                            Vec3(x_max, y_max, 0)
                        ]
                        bbox = BoundingBox(corners)
                        sheets.append(Sheet(name="", bbox=bbox))

        return sheets

    def _detect_sheets_by_clustering(self) -> List[Sheet]:
        """使用空间聚类分析检测图纸区域"""
        if not HAS_SKLEARN:
            return []

        sheets = []

        # 收集所有实体的坐标点
        points = []
        for line in self.lines:
            points.extend([(line.dxf.start.x, line.dxf.start.y),
                          (line.dxf.end.x, line.dxf.end.y)])
        for text in self.texts:
            points.append((text.pos.x, text.pos.y))

        if len(points) < CLUSTERING_MIN_SAMPLES:
            return sheets

        # 执行DBSCAN聚类
        points_array = np.array(points)
        clustering = DBSCAN(eps=CLUSTERING_EPS, min_samples=CLUSTERING_MIN_SAMPLES)
        cluster_labels = clustering.fit_predict(points_array)

        # 为每个聚类创建图纸
        unique_labels = set(cluster_labels)
        unique_labels.discard(-1)  # 移除噪声点标签

        for label in unique_labels:
            cluster_points = points_array[cluster_labels == label]
            if len(cluster_points) > 0:
                x_coords = cluster_points[:, 0]
                y_coords = cluster_points[:, 1]

                # 创建包围盒
                corners = [
                    Vec3(x_coords.min(), y_coords.min(), 0),
                    Vec3(x_coords.max(), y_coords.max(), 0)
                ]
                bbox = BoundingBox(corners)

                # 检查尺寸是否合理
                if bbox.size.x > MIN_SHEET_DIMENSION / 2 and bbox.size.y > MIN_SHEET_DIMENSION / 2:
                    sheets.append(Sheet(name="", bbox=bbox))

        return sheets

    def _reconstruct_tables_in_sheet(self, sheet: Sheet) -> List[Table]:
        """在单个图纸区域内，通过几何网格线重建所有表格"""
        # 筛选出在图纸范围内的横平竖直线
        h_lines, v_lines = [], []
        h_line_segments, v_line_segments = [], []

        for line in self.lines:
            if sheet.bbox.inside(line.dxf.start) and sheet.bbox.inside(line.dxf.end):
                p1, p2 = line.dxf.start, line.dxf.end
                if abs(p1.y - p2.y) < GEOMETRY_TOLERANCE:
                    # 水平线
                    y_coord = (p1.y + p2.y) / 2
                    h_lines.append(y_coord)
                    h_line_segments.append((min(p1.x, p2.x), max(p1.x, p2.x), y_coord))
                elif abs(p1.x - p2.x) < GEOMETRY_TOLERANCE:
                    # 垂直线
                    x_coord = (p1.x + p2.x) / 2
                    v_lines.append(x_coord)
                    v_line_segments.append((min(p1.y, p2.y), max(p1.y, p2.y), x_coord))

        if not h_lines or not v_lines:
            return []

        # 对坐标进行去重和排序
        y_coords = sorted(list(set(h_lines)), reverse=True)
        x_coords = sorted(list(set(v_lines)))

        # 合并非常接近的线 (处理粗线框)
        y_grid = self._merge_close_coords(y_coords, TABLE_GRID_TOLERANCE)
        x_grid = self._merge_close_coords(x_coords, TABLE_GRID_TOLERANCE)

        # 尝试识别多个独立的表格区域
        tables = self._identify_separate_tables(x_grid, y_grid, h_line_segments, v_line_segments, sheet)

        return tables

    def _identify_separate_tables(self, x_grid: List[float], y_grid: List[float],
                                h_line_segments: List[Tuple], v_line_segments: List[Tuple],
                                sheet: Sheet) -> List[Table]:
        """识别多个独立的表格区域"""
        tables = []

        if len(y_grid) < 2 or len(x_grid) < 2:
            return tables

        # 创建网格交点矩阵，标记哪些交点有实际的网格线连接
        grid_connections = {}

        # 标记水平连接
        for x1, x2, y in h_line_segments:
            for i, grid_y in enumerate(y_grid):
                if abs(y - grid_y) < TABLE_GRID_TOLERANCE:
                    for j in range(len(x_grid) - 1):
                        if (x_grid[j] >= x1 - TABLE_GRID_TOLERANCE and
                            x_grid[j + 1] <= x2 + TABLE_GRID_TOLERANCE):
                            grid_connections[(i, j, 'h')] = True

        # 标记垂直连接
        for y1, y2, x in v_line_segments:
            for j, grid_x in enumerate(x_grid):
                if abs(x - grid_x) < TABLE_GRID_TOLERANCE:
                    for i in range(len(y_grid) - 1):
                        if (y_grid[i] >= y2 - TABLE_GRID_TOLERANCE and
                            y_grid[i + 1] <= y1 + TABLE_GRID_TOLERANCE):
                            grid_connections[(i, j, 'v')] = True

        # 寻找连通的表格区域
        visited = set()

        for start_i in range(len(y_grid) - 1):
            for start_j in range(len(x_grid) - 1):
                if (start_i, start_j) in visited:
                    continue

                # 使用BFS找到连通的表格区域
                table_cells = self._find_connected_table_region(
                    start_i, start_j, x_grid, y_grid, grid_connections, visited
                )

                if len(table_cells) >= 4:  # 至少2x2的表格
                    table = self._create_table_from_cells(table_cells, x_grid, y_grid, sheet)
                    if table:
                        tables.append(table)

        # 如果没有找到独立的表格，创建一个包含所有网格的大表格
        if not tables:
            table = self._create_single_large_table(x_grid, y_grid, sheet)
            if table:
                tables.append(table)

        return tables

    def _find_connected_table_region(self, start_i: int, start_j: int,
                                   x_grid: List[float], y_grid: List[float],
                                   grid_connections: Dict, visited: set) -> List[Tuple[int, int]]:
        """使用BFS找到连通的表格区域"""
        from collections import deque

        queue = deque([(start_i, start_j)])
        region_cells = []
        local_visited = set()

        while queue:
            i, j = queue.popleft()
            if (i, j) in local_visited or (i, j) in visited:
                continue

            local_visited.add((i, j))
            region_cells.append((i, j))

            # 检查四个方向的连接
            # 上方
            if i > 0 and (i-1, j, 'h') in grid_connections:
                queue.append((i-1, j))
            # 下方
            if i < len(y_grid) - 2 and (i, j, 'h') in grid_connections:
                queue.append((i+1, j))
            # 左方
            if j > 0 and (i, j-1, 'v') in grid_connections:
                queue.append((i, j-1))
            # 右方
            if j < len(x_grid) - 2 and (i, j, 'v') in grid_connections:
                queue.append((i, j+1))

        # 将本地访问的单元格添加到全局访问集合
        visited.update(local_visited)
        return region_cells

    def _create_table_from_cells(self, cells: List[Tuple[int, int]],
                               x_grid: List[float], y_grid: List[float],
                               sheet: Sheet) -> Optional[Table]:
        """从单元格列表创建表格对象"""
        if not cells:
            return None

        # 计算表格边界
        min_i = min(cell[0] for cell in cells)
        max_i = max(cell[0] for cell in cells)
        min_j = min(cell[1] for cell in cells)
        max_j = max(cell[1] for cell in cells)

        # 创建表格边界框
        table_corners = [
            Vec3(x_grid[min_j], y_grid[max_i + 1], 0),  # 左下角
            Vec3(x_grid[max_j + 1], y_grid[max_i + 1], 0), # 右下角
            Vec3(x_grid[max_j + 1], y_grid[min_i], 0),  # 右上角
            Vec3(x_grid[min_j], y_grid[min_i], 0)    # 左上角
        ]
        table_bbox = BoundingBox(table_corners)

        # 创建表格对象
        num_rows = max_i - min_i + 1
        num_cols = max_j - min_j + 1
        table = Table(bbox=table_bbox, rows=num_rows, cols=num_cols)
        table.x_grid = x_grid[min_j:max_j + 2]
        table.y_grid = y_grid[min_i:max_i + 2]
        table.data = [[None for _ in range(num_cols)] for _ in range(num_rows)]

        # 填充文本数据
        self._fill_table_with_text(table, sheet, min_i, min_j)

        # 检测合并单元格
        table.merged_cells = self._detect_merged_cells(table)

        # 分类表格
        self._classify_table(table)

        return table

    def _create_single_large_table(self, x_grid: List[float], y_grid: List[float],
                                 sheet: Sheet) -> Optional[Table]:
        """创建包含所有网格的单一大表格"""
        if len(y_grid) < 2 or len(x_grid) < 2:
            return None

        # 创建表格边界框
        table_corners = [
            Vec3(x_grid[0], y_grid[-1], 0),  # 左下角
            Vec3(x_grid[-1], y_grid[-1], 0), # 右下角
            Vec3(x_grid[-1], y_grid[0], 0),  # 右上角
            Vec3(x_grid[0], y_grid[0], 0)    # 左上角
        ]
        table_bbox = BoundingBox(table_corners)

        # 创建表格对象
        num_rows, num_cols = len(y_grid) - 1, len(x_grid) - 1
        table = Table(bbox=table_bbox, rows=num_rows, cols=num_cols)
        table.x_grid = x_grid
        table.y_grid = y_grid
        table.data = [[None for _ in range(num_cols)] for _ in range(num_rows)]

        # 填充文本数据
        self._fill_table_with_text(table, sheet, 0, 0)

        # 检测合并单元格
        table.merged_cells = self._detect_merged_cells(table)

        # 分类表格
        self._classify_table(table)

        return table

    def _fill_table_with_text(self, table: Table, sheet: Sheet, offset_i: int, offset_j: int):
        """填充表格的文本内容"""
        sheet_texts = [text for text in self.texts if sheet.bbox.inside(text.pos)]

        for text in sheet_texts:
            if not table.bbox.inside(text.pos):
                continue

            # 找到文本所属的单元格
            row_idx, col_idx = -1, -1
            for i in range(table.rows):
                grid_i = i + offset_i
                if (grid_i + 1 < len(table.y_grid) and
                    table.y_grid[grid_i + 1] <= text.pos.y <= table.y_grid[grid_i]):
                    row_idx = i
                    break

            for j in range(table.cols):
                grid_j = j + offset_j
                if (grid_j + 1 < len(table.x_grid) and
                    table.x_grid[grid_j] <= text.pos.x <= table.x_grid[grid_j + 1]):
                    col_idx = j
                    break

            if row_idx != -1 and col_idx != -1:
                # 如果单元格已有内容，则追加
                if table.data[row_idx][col_idx]:
                    table.data[row_idx][col_idx].content += f" {text.content}"
                else:
                    table.data[row_idx][col_idx] = text

    def _detect_merged_cells(self, table: Table) -> List[Tuple[int, int, int, int]]:
        """检测合并单元格"""
        merged_cells = []

        # 简单的合并单元格检测：如果一个较大的矩形区域内只有一个文本，
        # 且该区域跨越多个网格单元格，则认为是合并单元格
        for i in range(table.rows):
            for j in range(table.cols):
                if table.data[i][j] is not None:
                    # 检查是否可以向右或向下扩展
                    max_right = j
                    max_down = i

                    # 向右扩展
                    while (max_right + 1 < table.cols and
                           table.data[i][max_right + 1] is None):
                        max_right += 1

                    # 向下扩展
                    while (max_down + 1 < table.rows and
                           all(table.data[max_down + 1][k] is None
                               for k in range(j, max_right + 1))):
                        max_down += 1

                    # 如果扩展了，则认为是合并单元格
                    if max_right > j or max_down > i:
                        merged_cells.append((i, j, max_down, max_right))

                        # 标记被合并的单元格
                        for mi in range(i, max_down + 1):
                            for mj in range(j, max_right + 1):
                                if mi != i or mj != j:
                                    table.data[mi][mj] = None  # 清空被合并的单元格

        return merged_cells

    def _classify_table(self, table: Table):
        """对表格进行分类并设置标题"""
        table_text = table.get_full_text()

        if any(kw in table_text for kw in KEYWORD_LEGEND):
            table.category = "图例表格"
            table.title = "图例说明"
        elif any(kw in table_text for kw in KEYWORD_TABLE):
            table.category = "工程表格"
            table.title = "工程信息表"
        else:
            # 尝试从第一行文字推断标题
            first_row_text = ""
            for cell in table.data[0] if table.data else []:
                if cell:
                    first_row_text += cell.content + " "

            if first_row_text.strip():
                table.title = first_row_text.strip()[:30]  # 取前30个字符作标题
            else:
                table.title = f"数据表格 ({table.rows}x{table.cols})"

    def _merge_close_coords(self, coords: List[float], tolerance: float) -> List[float]:
        """合并排序后列表中的相近值"""
        if not coords:
            return []
        merged = [coords[0]]
        for i in range(1, len(coords)):
            if abs(coords[i] - merged[-1]) > tolerance:
                merged.append(coords[i])
        return merged

    def _assign_entities_to_sheet(self, sheet: Sheet):
        """将实体分类到图纸的主图、图例等区域"""
        # 获取已分配到表格的实体位置
        table_entity_positions = {t.pos for table in sheet.tables for row in table.data for t in row if t}

        for text in self.texts:
            if not sheet.bbox.inside(text.pos) or text.pos in table_entity_positions:
                continue

            entity_info = {"类型": "文本", "内容": text.content, "图层": text.layer}
            # 基于文本内容和图层名进行简单分类
            if any(kw in text.content or kw in text.layer for kw in KEYWORD_LEGEND):
                sheet.legend_entities.append(entity_info)
            else:
                sheet.main_drawing_entities.append(entity_info)

        # 分配非文本的几何图形 (简化)
        # 此处可以添加更复杂的逻辑来分配线、圆等
        sheet.main_drawing_entities.append(
            {"类型": "统计", "内容": f"包含 {len(self.lines)} 条线、{len(self.polylines)} 条多段线等几何图形"})

    def _generate_final_output(self, sheets: List[Sheet]) -> Dict[str, Any]:
        """构建最终的JSON和Markdown输出"""
        output = {
            "文件元数据": {
                "文件名": os.path.basename(self.dxf_path),
                "DXF版本": self.doc.dxfversion,
                "图纸数量": len(sheets)
            },
            "图纸结构": []
        }

        markdown_output = [f"# DXF解析报告: {os.path.basename(self.dxf_path)}\n"]

        for sheet in sheets:
            sheet_data = {
                "图纸名称": sheet.name,
                "主图内容": sheet.main_drawing_entities,
                "图例内容": sheet.legend_entities,
                "表格列表": []
            }
            markdown_output.append(f"## {sheet.name}\n")

            if not sheet.tables:
                markdown_output.append("此图纸未检测到表格。\n")
            else:
                for table in sheet.tables:
                    table_json, table_md = self._format_table(table)
                    sheet_data["表格列表"].append(table_json)
                    markdown_output.append(table_md)

            output["图纸结构"].append(sheet_data)

        output["markdown报告"] = "".join(markdown_output)
        return output

    def _format_table(self, table: Table) -> Tuple[Dict, str]:
        """将单个表格格式化为JSON和Markdown"""
        # 准备JSON数据
        json_data = {
            "表格标题": table.title,
            "表格类型": table.category,
            "行数": table.rows,
            "列数": table.cols,
            "合并单元格": table.merged_cells,
            "数据": [
                [cell.content if cell else "" for cell in row]
                for row in table.data
            ],
            "网格坐标": {
                "x_grid": table.x_grid,
                "y_grid": table.y_grid
            } if table.x_grid and table.y_grid else None
        }

        # 准备Markdown数据
        headers = [f"列 {i + 1}" for i in range(table.cols)]
        md_lines = [
            f"### {table.title} ({table.category})\n",
            f"**尺寸**: {table.rows} 行 × {table.cols} 列\n"
        ]

        # 如果有合并单元格，添加说明
        if table.merged_cells:
            md_lines.append(f"**合并单元格**: {len(table.merged_cells)} 个\n")

        # 创建表格
        md_lines.extend([
            "| " + " | ".join(headers) + " |",
            "| " + " | ".join(["---"] * len(headers)) + " |"
        ])

        for row_data in json_data["数据"]:
            cleaned_row = [str(cell).replace("|", "\\|").replace("\n", " ") for cell in row_data]
            md_lines.append("| " + " | ".join(cleaned_row) + " |")

        md_lines.append("\n")

        return json_data, "\n".join(md_lines)

    def export_tables_to_files(self, sheets: List[Sheet], output_dir: str):
        """导出所有表格到Excel和CSV文件"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        table_count = 0
        for sheet_idx, sheet in enumerate(sheets):
            for table_idx, table in enumerate(sheet.tables):
                table_count += 1
                base_filename = f"table_{table_count:02d}_{sheet.name}_表格{table_idx + 1}"

                # 导出到Excel
                excel_path = os.path.join(output_dir, f"{base_filename}.xlsx")
                if table.export_to_excel(excel_path):
                    print(f"表格已导出到Excel: {excel_path}")

                # 导出到CSV
                csv_path = os.path.join(output_dir, f"{base_filename}.csv")
                if table.export_to_csv(csv_path):
                    print(f"表格已导出到CSV: {csv_path}")

        return table_count


def main():
    """主执行函数"""
    # --- 请在这里配置您的输入和输出路径 ---
    # 输入可以是单个DXF文件，也可以是一个包含DXF文件的文件夹
    # 示例: input_path = 'C:/Users/<USER>/Desktop/dxf_drawings'
    # 示例: input_path = 'C:/Users/<USER>/Desktop/single_drawing.dxf'
    input_path = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test'
    # --- 配置结束 ---

    input_path = Path(input_path)
    if not input_path.exists():
        print(f"错误: 输入路径不存在 -> {input_path}")
        return

    if input_path.is_dir():
        output_dir = input_path.parent / f"{input_path.name}_parsed_results"
        dxf_files = list(input_path.glob('*.dxf')) + list(input_path.glob('*.DXF'))
    else:
        output_dir = input_path.parent / f"{input_path.stem}_parsed_results"
        dxf_files = [input_path]

    output_dir.mkdir(exist_ok=True)

    if not dxf_files:
        print(f"错误: 在路径 {input_path} 中未找到任何DXF文件。")
        return

    print(f"找到 {len(dxf_files)} 个DXF文件，结果将保存在: {output_dir}")

    for dxf_file in tqdm(dxf_files, desc="正在解析DXF文件"):
        parser = DXFStructureParser(str(dxf_file))
        result = parser.parse()

        # 生成输出文件名
        json_output_path = output_dir / f"{dxf_file.stem}_structure.json"
        md_output_path = output_dir / f"{dxf_file.stem}_report.md"

        if "错误" in result:
            print(f"\n[失败] 解析: {dxf_file.name} - {result['错误']}")
            continue

        try:
            # 提取Markdown报告并保存
            markdown_report = result.pop("markdown报告", "生成报告失败。")
            with open(md_output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_report)

            # 保存剩余的JSON数据
            with open(json_output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            # 导出表格到Excel和CSV文件
            if "图纸结构" in result and result["图纸结构"]:
                tables_output_dir = output_dir / f"{dxf_file.stem}_tables"
                # 重新创建解析器来获取sheets对象（因为result中的数据已经序列化）
                temp_parser = DXFStructureParser(str(dxf_file))
                temp_result = temp_parser.parse()
                if "错误" not in temp_result:
                    # 重新解析以获取sheets对象
                    temp_parser._extract_all_entities()
                    sheets = temp_parser._detect_sheets()
                    if sheets:
                        for sheet in sheets:
                            sheet.tables = temp_parser._reconstruct_tables_in_sheet(sheet)

                        table_count = temp_parser.export_tables_to_files(sheets, str(tables_output_dir))
                        if table_count > 0:
                            print(f"[导出] {dxf_file.name}: 成功导出 {table_count} 个表格")

            print(f"[成功] 解析: {dxf_file.name}")
        except Exception as e:
            print(f"\n[失败] 保存结果时出错: {dxf_file.name} - {e}")

    print("\n所有文件处理完毕。")


if __name__ == '__main__':
    main()