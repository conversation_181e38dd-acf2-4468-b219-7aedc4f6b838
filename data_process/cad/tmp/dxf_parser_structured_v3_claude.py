import ezdxf
import json
import re
import os
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Any, Optional, Tuple, Union
import numpy as np
from tqdm import tqdm


def safe_coordinate_conversion(coord) -> List[float]:
    """安全地将坐标对象转换为列表"""
    if coord is None:
        return []

    try:
        if isinstance(coord, (list, tuple)):
            return [float(x) for x in coord]

        if hasattr(coord, 'x') and hasattr(coord, 'y'):
            z = getattr(coord, 'z', 0.0)
            return [float(coord.x), float(coord.y), float(z)]

        if hasattr(coord, '__iter__'):
            return [float(x) for x in coord]

        return [float(coord)]

    except Exception as e:
        print(f"坐标转换警告: {e}")
        return []


def safe_json_conversion(obj) -> Any:
    """安全地将对象转换为JSON可序列化的格式"""
    if obj is None:
        return None

    try:
        if isinstance(obj, (str, int, float, bool)):
            return obj

        if isinstance(obj, (list, tuple)):
            return [safe_json_conversion(item) for item in obj]

        if isinstance(obj, dict):
            return {key: safe_json_conversion(value) for key, value in obj.items()}

        if hasattr(obj, 'x') and hasattr(obj, 'y'):
            z = getattr(obj, 'z', 0.0)
            return [float(obj.x), float(obj.y), float(z)]

        if hasattr(obj, '__iter__') and not isinstance(obj, str):
            return [safe_json_conversion(item) for item in obj]

        return str(obj)

    except Exception as e:
        return str(obj)


class DXFAdvancedParser:
    """
    高级DXF解析器 - 基于空间聚类和网格线分析的方法
    """

    def __init__(self, dxf_path: str, include_coordinates: bool = False):
        self.dxf_path = dxf_path
        self.include_coordinates = include_coordinates
        self.doc = None

        # 存储所有实体的原始信息
        self.all_entities = []
        self.text_entities = []
        self.line_entities = []

        # 图纸区域
        self.drawing_sheets = []

        # 容差参数
        self.line_tolerance = 10.0  # 判断线是否水平/垂直的容差
        self.grid_tolerance = 5.0  # 网格线聚类的容差
        self.cell_tolerance = 2.0  # 文本归属单元格的容差

    def load_document(self) -> bool:
        """加载DXF文档"""
        try:
            self.doc = ezdxf.readfile(self.dxf_path)
            return True
        except Exception as e:
            print(f"错误: 无法读取DXF文件: {e}")
            return False

    def clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        text = re.sub(r'\\[A-Za-z][0-9]*;?', '', text)
        text = re.sub(r'\\[{}]', '', text)
        text = re.sub(r'\\P', '\n', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()

    def extract_all_entities(self):
        """第一步：提取所有实体的坐标和信息"""
        self.all_entities = []
        self.text_entities = []
        self.line_entities = []

        # 处理模型空间
        for entity in self.doc.modelspace():
            self._process_entity(entity, "模型空间")

        # 处理图纸空间
        for layout in self.doc.layouts:
            if layout.name != 'Model':
                for entity in layout:
                    self._process_entity(entity, f"图纸空间-{layout.name}")

        print(
            f"提取完成: 总实体数={len(self.all_entities)}, 文本实体数={len(self.text_entities)}, 线实体数={len(self.line_entities)}")

    def _process_entity(self, entity, space_name: str):
        """处理单个实体"""
        entity_type = entity.dxftype()
        entity_info = {
            "实体类型": entity_type,
            "所在图层": getattr(entity.dxf, 'layer', ''),
            "颜色索引": getattr(entity.dxf, 'color', 0),
            "空间名称": space_name
        }

        # 处理文本实体
        if entity_type in ['TEXT', 'MTEXT', 'ATTRIB', 'ATTDEF']:
            text_info = self._extract_text_entity(entity)
            if text_info:
                entity_info.update(text_info)
                self.text_entities.append(entity_info)

        # 处理块实体
        elif entity_type == 'INSERT':
            block_info = self._extract_block_entity(entity)
            if block_info:
                entity_info.update(block_info)
                # 如果块有属性文本，也加入文本实体列表
                if block_info.get("属性文本"):
                    self.text_entities.append(entity_info)

        # 处理线实体
        elif entity_type == 'LINE':
            line_info = self._extract_line_entity(entity)
            if line_info:
                entity_info.update(line_info)
                self.line_entities.append(entity_info)

        # 处理其他几何实体
        elif entity_type in ['CIRCLE', 'ARC', 'LWPOLYLINE', 'POLYLINE', 'ELLIPSE', 'SPLINE']:
            geo_info = self._extract_geometric_entity(entity)
            if geo_info:
                entity_info.update(geo_info)

        self.all_entities.append(entity_info)

    def _extract_text_entity(self, entity) -> Dict:
        """提取文本实体信息"""
        entity_type = entity.dxftype()
        text_info = {}

        try:
            if entity_type == 'TEXT':
                text_info["文本内容"] = self.clean_text(entity.dxf.text)
                text_info["插入点"] = safe_coordinate_conversion(entity.dxf.insert)
                text_info["文本高度"] = getattr(entity.dxf, 'height', 0)
                text_info["旋转角度"] = getattr(entity.dxf, 'rotation', 0)

            elif entity_type == 'MTEXT':
                text_info["文本内容"] = self.clean_text(entity.plain_text())
                text_info["插入点"] = safe_coordinate_conversion(entity.dxf.insert)
                text_info["字符高度"] = getattr(entity.dxf, 'char_height', 0)
                text_info["旋转角度"] = getattr(entity.dxf, 'rotation', 0)

            elif entity_type in ['ATTRIB', 'ATTDEF']:
                text_info["文本内容"] = self.clean_text(entity.dxf.text)
                text_info["插入点"] = safe_coordinate_conversion(entity.dxf.insert)
                text_info["标签"] = getattr(entity.dxf, 'tag', '')
                text_info["文本高度"] = getattr(entity.dxf, 'height', 0)

        except Exception as e:
            text_info["提取错误"] = str(e)

        return text_info if text_info.get("文本内容") else None

    def _extract_block_entity(self, entity) -> Dict:
        """提取块实体信息"""
        block_info = {}

        try:
            block_info["块名称"] = getattr(entity.dxf, 'name', '')
            block_info["插入点"] = safe_coordinate_conversion(entity.dxf.insert)
            block_info["缩放比例"] = [
                getattr(entity.dxf, 'xscale', 1.0),
                getattr(entity.dxf, 'yscale', 1.0),
                getattr(entity.dxf, 'zscale', 1.0)
            ]
            block_info["旋转角度"] = getattr(entity.dxf, 'rotation', 0)

            # 提取块属性
            attribute_texts = []
            if hasattr(entity, 'attribs'):
                for attrib in entity.attribs:
                    if hasattr(attrib.dxf, 'text') and attrib.dxf.text:
                        attr_text = self.clean_text(attrib.dxf.text)
                        if attr_text:
                            attribute_texts.append(attr_text)

            if attribute_texts:
                block_info["属性文本"] = "; ".join(attribute_texts)
                block_info["文本内容"] = block_info["属性文本"]  # 为了统一处理

        except Exception as e:
            block_info["提取错误"] = str(e)

        return block_info

    def _extract_line_entity(self, entity) -> Dict:
        """提取线实体信息"""
        line_info = {}

        try:
            start_point = safe_coordinate_conversion(entity.dxf.start)
            end_point = safe_coordinate_conversion(entity.dxf.end)

            if len(start_point) >= 2 and len(end_point) >= 2:
                line_info["起点"] = start_point
                line_info["终点"] = end_point

                # 计算线的方向
                dx = end_point[0] - start_point[0]
                dy = end_point[1] - start_point[1]
                length = (dx ** 2 + dy ** 2) ** 0.5

                line_info["长度"] = length
                line_info["方向向量"] = [dx, dy]

                # 判断是否为水平线或垂直线
                if length > 0:
                    if abs(dy) < self.line_tolerance:
                        line_info["线型"] = "水平线"
                        line_info["Y坐标"] = (start_point[1] + end_point[1]) / 2
                        line_info["X范围"] = [min(start_point[0], end_point[0]), max(start_point[0], end_point[0])]
                    elif abs(dx) < self.line_tolerance:
                        line_info["线型"] = "垂直线"
                        line_info["X坐标"] = (start_point[0] + end_point[0]) / 2
                        line_info["Y范围"] = [min(start_point[1], end_point[1]), max(start_point[1], end_point[1])]
                    else:
                        line_info["线型"] = "斜线"

        except Exception as e:
            line_info["提取错误"] = str(e)

        return line_info

    def _extract_geometric_entity(self, entity) -> Dict:
        """提取几何实体信息"""
        geo_info = {}
        entity_type = entity.dxftype()

        try:
            if entity_type == 'CIRCLE':
                geo_info["几何类型"] = "圆"
                geo_info["圆心"] = safe_coordinate_conversion(entity.dxf.center)
                geo_info["半径"] = entity.dxf.radius

            elif entity_type == 'ARC':
                geo_info["几何类型"] = "圆弧"
                geo_info["圆心"] = safe_coordinate_conversion(entity.dxf.center)
                geo_info["半径"] = entity.dxf.radius
                geo_info["起始角度"] = entity.dxf.start_angle
                geo_info["结束角度"] = entity.dxf.end_angle

        except Exception as e:
            geo_info["提取错误"] = str(e)

        return geo_info

    def detect_drawing_sheets_by_clustering(self) -> List[Dict]:
        """第二步：通过空间聚类分析识别图纸区域"""
        if not self.text_entities:
            return []

        # 提取所有文本实体的坐标
        text_coords = []
        for entity in self.text_entities:
            if len(entity.get("文本内容"))<1:continue
            coords = entity.get("插入点", [])
            if len(coords) >= 2:
                text_coords.append([coords[0], coords[1]])

        if len(text_coords) < 10:
            print("文本实体数量太少，无法进行聚类分析")
            return []

        text_coords = np.array(text_coords)

        # 分析X坐标分布，寻找空白区域
        x_coords = text_coords[:, 0]
        x_min, x_max = x_coords.min(), x_coords.max()
        x_range = x_max - x_min

        print(f"X坐标范围: {x_min:.2f} 到 {x_max:.2f}, 总范围: {x_range:.2f}")

        # 如果X范围很小，可能是单个图纸
        if x_range < 50000:
            print('如果X范围很小，可能是单个图纸')
            y_coords = text_coords[:, 1]
            return [{
                "图纸名称": "图纸1",
                "图纸类型": "单页图纸",
                "X范围": [float(x_min), float(x_max)],
                "Y范围": [float(y_coords.min()), float(y_coords.max())],
                "文本数量": len(text_coords)
            }]

        # 分析X坐标的密度分布，寻找空白区域
        sheets = self._find_sheets_by_gap_analysis(text_coords, x_range)

        if len(sheets) == 0:
            # 回退到单图纸
            y_coords = text_coords[:, 1]
            sheets = [{
                "图纸名称": "图纸1",
                "图纸类型": "单页图纸",
                "X范围": [float(x_min), float(x_max)],
                "Y范围": [float(y_coords.min()), float(y_coords.max())],
                "文本数量": len(text_coords)
            }]

        self.drawing_sheets = sheets
        print(f"检测到 {len(sheets)} 个图纸区域")
        for i, sheet in enumerate(sheets):
            print(
                f"  {sheet['图纸名称']}: X范围={sheet['X范围']}, Y范围={sheet['Y范围']}, 文本数量={sheet['文本数量']}")

        return sheets

    def _find_sheets_by_gap_analysis(self, text_coords: np.ndarray, x_range: float) -> List[Dict]:
        """通过间隙分析寻找图纸边界"""
        x_coords = text_coords[:, 0]

        # 创建X坐标的直方图
        num_bins = max(50, int(x_range / 100))  # 动态确定bin数量
        hist, bin_edges = np.histogram(x_coords, bins=num_bins)

        # 寻找空白区域（直方图值为0或很小的区域）
        threshold = max(1, len(text_coords) * 0.001)  # 动态阈值
        empty_bins = hist < threshold

        # 找到连续的空白区域
        gaps = []
        in_gap = False
        gap_start = None

        for i, is_empty in enumerate(empty_bins):
            if is_empty and not in_gap:
                # 开始一个新的空白区域
                in_gap = True
                gap_start = bin_edges[i]
            elif not is_empty and in_gap:
                # 结束当前空白区域
                gap_end = bin_edges[i]
                gap_width = gap_end - gap_start
                if gap_width > x_range * 0.05:  # 空白区域宽度超过总范围的5%
                    gaps.append((gap_start, gap_end, gap_width))
                in_gap = False

        # 处理最后一个空白区域
        if in_gap:
            gap_end = bin_edges[-1]
            gap_width = gap_end - gap_start
            if gap_width > x_range * 0.05:
                gaps.append((gap_start, gap_end, gap_width))

        print(f"发现 {len(gaps)} 个空白区域")
        for gap in gaps:
            print(f"  空白区域: {gap[0]:.2f} 到 {gap[1]:.2f}, 宽度: {gap[2]:.2f}")

        # 如果没有找到明显的空白区域，尝试使用聚类
        if len(gaps) == 0:
            return self._cluster_by_kmeans(text_coords)

        # 根据最大的空白区域分割图纸
        if gaps:
            # 选择最大的空白区域作为分割点
            largest_gap = max(gaps, key=lambda x: x[2])
            split_x = (largest_gap[0] + largest_gap[1]) / 2

            # 分割文本坐标
            left_coords = text_coords[text_coords[:, 0] < split_x]
            right_coords = text_coords[text_coords[:, 0] >= split_x]

            sheets = []
            if len(left_coords) > 10:
                sheets.append({
                    "图纸名称": "图纸1",
                    "图纸类型": "左侧图纸",
                    "X范围": [float(left_coords[:, 0].min()), float(left_coords[:, 0].max())],
                    "Y范围": [float(left_coords[:, 1].min()), float(left_coords[:, 1].max())],
                    "文本数量": len(left_coords)
                })

            if len(right_coords) > 10:
                sheets.append({
                    "图纸名称": "图纸2",
                    "图纸类型": "右侧图纸",
                    "X范围": [float(right_coords[:, 0].min()), float(right_coords[:, 0].max())],
                    "Y范围": [float(right_coords[:, 1].min()), float(right_coords[:, 1].max())],
                    "文本数量": len(right_coords)
                })

            return sheets

        return []

    def _cluster_by_kmeans(self, text_coords: np.ndarray) -> List[Dict]:
        """使用K-means聚类分析"""
        try:
            from sklearn.cluster import KMeans

            # 尝试2个聚类
            kmeans = KMeans(n_clusters=2, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(text_coords)

            cluster_0_coords = text_coords[cluster_labels == 0]
            cluster_1_coords = text_coords[cluster_labels == 1]

            # 检查聚类质量
            if len(cluster_0_coords) < 10 or len(cluster_1_coords) < 10:
                return []

            # 按X坐标排序
            center_0_x = np.mean(cluster_0_coords[:, 0])
            center_1_x = np.mean(cluster_1_coords[:, 0])

            if center_0_x < center_1_x:
                left_coords, right_coords = cluster_0_coords, cluster_1_coords
            else:
                left_coords, right_coords = cluster_1_coords, cluster_0_coords

            return [
                {
                    "图纸名称": "图纸1",
                    "图纸类型": "左侧图纸",
                    "X范围": [float(left_coords[:, 0].min()), float(left_coords[:, 0].max())],
                    "Y范围": [float(left_coords[:, 1].min()), float(left_coords[:, 1].max())],
                    "文本数量": len(left_coords)
                },
                {
                    "图纸名称": "图纸2",
                    "图纸类型": "右侧图纸",
                    "X范围": [float(right_coords[:, 0].min()), float(right_coords[:, 0].max())],
                    "Y范围": [float(right_coords[:, 1].min()), float(right_coords[:, 1].max())],
                    "文本数量": len(right_coords)
                }
            ]

        except ImportError:
            print("sklearn未安装，无法使用K-means聚类")
            return []

    def detect_tables_in_sheets(self) -> List[Dict]:
        """第三步：在每个图纸区域内检测表格"""
        for sheet in self.drawing_sheets:
            print(f"\n分析 {sheet['图纸名称']} 的表格...")

            # 获取该图纸区域内的线实体和文本实体
            sheet_lines = self._get_lines_in_sheet(sheet)
            sheet_texts = self._get_texts_in_sheet(sheet)

            print(f"  图纸内线实体数: {len(sheet_lines)}, 文本实体数: {len(sheet_texts)}")

            # 分析网格线，检测表格
            tables = self._detect_tables_by_grid_analysis(sheet_lines, sheet_texts, sheet)

            # 将表格信息添加到图纸中
            sheet["表格列表"] = tables
            sheet["表格数量"] = len(tables)

            # 剩余的文本归类为主图
            main_drawing_texts = []
            table_text_ids = set()

            # 收集所有表格中的文本ID
            for table in tables:
                for row in table.get("单元格数据", []):
                    for cell in row:
                        if cell.get("文本实体"):
                            table_text_ids.add(id(cell["文本实体"]))

            # 找出不在表格中的文本
            for text in sheet_texts:
                if id(text) not in table_text_ids:
                    main_drawing_texts.append(text)

            sheet["主图文本"] = main_drawing_texts
            sheet["主图文本数量"] = len(main_drawing_texts)

            print(f"  检测到表格数: {len(tables)}, 主图文本数: {len(main_drawing_texts)}")

        return self.drawing_sheets

    def _get_lines_in_sheet(self, sheet: Dict) -> List[Dict]:
        """获取图纸区域内的线实体"""
        x_min, x_max = sheet["X范围"]
        y_min, y_max = sheet["Y范围"]

        sheet_lines = []
        for line in self.line_entities:
            start_point = line.get("起点", [])
            end_point = line.get("终点", [])

            if len(start_point) >= 2 and len(end_point) >= 2:
                # 检查线是否在图纸范围内
                line_x_min = min(start_point[0], end_point[0])
                line_x_max = max(start_point[0], end_point[0])
                line_y_min = min(start_point[1], end_point[1])
                line_y_max = max(start_point[1], end_point[1])

                # 判断线是否与图纸区域有重叠
                if (line_x_max >= x_min and line_x_min <= x_max and
                        line_y_max >= y_min and line_y_min <= y_max):
                    sheet_lines.append(line)

        return sheet_lines

    def _get_texts_in_sheet(self, sheet: Dict) -> List[Dict]:
        """获取图纸区域内的文本实体"""
        x_min, x_max = sheet["X范围"]
        y_min, y_max = sheet["Y范围"]

        sheet_texts = []
        for text in self.text_entities:
            coords = text.get("插入点", [])
            if len(coords) >= 2:
                x, y = coords[0], coords[1]
                if x_min <= x <= x_max and y_min <= y <= y_max:
                    sheet_texts.append(text)

        return sheet_texts

    def _detect_tables_by_grid_analysis(self, lines: List[Dict], texts: List[Dict], sheet: Dict) -> List[Dict]:
        """第四步：通过网格线分析检测表格"""
        if len(lines) < 4:  # 至少需要4条线才能形成表格
            return []

        # 分离水平线和垂直线
        horizontal_lines = [line for line in lines if line.get("线型") == "水平线"]
        vertical_lines = [line for line in lines if line.get("线型") == "垂直线"]

        print(f"    水平线数: {len(horizontal_lines)}, 垂直线数: {len(vertical_lines)}")

        if len(horizontal_lines) < 2 or len(vertical_lines) < 2:
            return []

        # 聚类分析，找到密集的网格区域
        table_regions = self._find_grid_regions(horizontal_lines, vertical_lines)

        tables = []
        for i, region in enumerate(table_regions):
            print(f"    分析表格区域 {i + 1}: {region['边界']}")

            # 重建表格结构
            table = self._reconstruct_table(region, texts, i + 1)
            if table:
                tables.append(table)

        return tables

    def _find_grid_regions(self, horizontal_lines: List[Dict], vertical_lines: List[Dict]) -> List[Dict]:
        """寻找密集的网格区域"""
        if not horizontal_lines or not vertical_lines:
            return []

        # 提取水平线的Y坐标和X范围
        h_lines_data = []
        for line in horizontal_lines:
            if "Y坐标" in line and "X范围" in line:
                h_lines_data.append({
                    "Y": line["Y坐标"],
                    "X_range": line["X范围"],
                    "line": line
                })

        # 提取垂直线的X坐标和Y范围
        v_lines_data = []
        for line in vertical_lines:
            if "X坐标" in line and "Y范围" in line:
                v_lines_data.append({
                    "X": line["X坐标"],
                    "Y_range": line["Y范围"],
                    "line": line
                })

        if len(h_lines_data) < 2 or len(v_lines_data) < 2:
            return []

        # 寻找交叉密集的区域
        regions = []

        # 简化版本：寻找所有线的重叠区域
        all_x_coords = [v["X"] for v in v_lines_data]
        all_y_coords = [h["Y"] for h in h_lines_data]

        if len(all_x_coords) >= 2 and len(all_y_coords) >= 2:
            x_min, x_max = min(all_x_coords), max(all_x_coords)
            y_min, y_max = min(all_y_coords), max(all_y_coords)

            # 检查这个区域内是否有足够的网格线
            region_h_lines = [h for h in h_lines_data
                              if (h["X_range"][0] <= x_max and h["X_range"][1] >= x_min)]
            region_v_lines = [v for v in v_lines_data
                              if (v["Y_range"][0] <= y_max and v["Y_range"][1] >= y_min)]

            if len(region_h_lines) >= 2 and len(region_v_lines) >= 2:
                regions.append({
                    "边界": [x_min, y_min, x_max, y_max],
                    "水平线": region_h_lines,
                    "垂直线": region_v_lines
                })

        return regions

    def _reconstruct_table(self, region: Dict, texts: List[Dict], table_id: int) -> Optional[Dict]:
        """重建表格结构"""
        h_lines = region["水平线"]
        v_lines = region["垂直线"]
        x_min, y_min, x_max, y_max = region["边界"]

        # 提取并排序行边界（Y坐标）
        row_boundaries = sorted(list(set([h["Y"] for h in h_lines])), reverse=True)  # 从上到下

        # 提取并排序列边界（X坐标）
        col_boundaries = sorted(list(set([v["X"] for v in v_lines])))  # 从左到右

        print(f"      行边界数: {len(row_boundaries)}, 列边界数: {len(col_boundaries)}")

        if len(row_boundaries) < 2 or len(col_boundaries) < 2:
            return None

        # 创建单元格网格
        rows = len(row_boundaries) - 1
        cols = len(col_boundaries) - 1

        cell_grid = []
        for r in range(rows):
            row_cells = []
            for c in range(cols):
                # 定义单元格边界
                cell_x_min = col_boundaries[c]
                cell_x_max = col_boundaries[c + 1]
                cell_y_max = row_boundaries[r]  # 注意：Y坐标是反向的
                cell_y_min = row_boundaries[r + 1]

                # 在单元格内查找文本
                cell_texts = []
                for text in texts:
                    coords = text.get("插入点", [])
                    if len(coords) >= 2:
                        x, y = coords[0], coords[1]
                        if (cell_x_min <= x <= cell_x_max and
                                cell_y_min <= y <= cell_y_max):
                            cell_texts.append(text["文本内容"])

                # 合并单元格内的文本
                cell_content = " ".join(cell_texts) if cell_texts else ""

                row_cells.append({
                    "内容": cell_content,
                    "边界": [cell_x_min, cell_y_min, cell_x_max, cell_y_max],
                    "文本数量": len(cell_texts),
                    "文本实体": cell_texts[0] if len(cell_texts) == 1 else None
                })

            cell_grid.append(row_cells)

        # 识别表格类型
        table_type = self._classify_table_type(cell_grid)

        table_info = {
            "表格ID": table_id,
            "表格名称": f"表格{table_id}",
            "表格类型": table_type,
            "边界": [x_min, y_min, x_max, y_max],
            "行数": rows,
            "列数": cols,
            "单元格数据": cell_grid,
            "行边界": row_boundaries,
            "列边界": col_boundaries
        }

        return table_info

    def _classify_table_type(self, cell_grid: List[List[Dict]]) -> str:
        """分类表格类型"""
        # 收集所有单元格内容
        all_content = []
        for row in cell_grid:
            for cell in row:
                content = cell.get("内容", "").strip()
                if content:
                    all_content.append(content.lower())

        content_text = " ".join(all_content)

        # 图例关键词
        legend_keywords = ["图例", "符号", "说明", "标识", "legend"]
        if any(keyword in content_text for keyword in legend_keywords):
            return "图例表格"

        # 工程信息关键词
        engineering_keywords = ["工程号", "项目号", "图号", "设计", "审核", "校对", "批准"]
        if any(keyword in content_text for keyword in engineering_keywords):
            return "工程信息表格"

        # 设备清单关键词
        equipment_keywords = ["设备", "模块", "清单", "型号", "规格", "数量"]
        if any(keyword in content_text for keyword in equipment_keywords):
            return "设备清单表格"

        # 项目信息关键词
        project_keywords = ["核电", "建设", "业主", "中广核", "三澳"]
        if any(keyword in content_text for keyword in project_keywords):
            return "项目信息表格"

        return "通用表格"

    def generate_markdown_output(self) -> str:
        """生成markdown格式的输出"""
        markdown_lines = []

        markdown_lines.append("# DXF图纸解析结果")
        markdown_lines.append("")
        markdown_lines.append(f"**文件名**: {os.path.basename(self.dxf_path)}")
        markdown_lines.append(f"**图纸数量**: {len(self.drawing_sheets)}")
        markdown_lines.append("")

        for sheet in self.drawing_sheets:
            sheet_name = sheet["图纸名称"]
            markdown_lines.append(f"## {sheet_name}")
            markdown_lines.append("")

            # 图纸基本信息
            markdown_lines.append(f"**图纸类型**: {sheet['图纸类型']}")
            markdown_lines.append(f"**X范围**: {sheet['X范围'][0]:.2f} ~ {sheet['X范围'][1]:.2f}")
            markdown_lines.append(f"**Y范围**: {sheet['Y范围'][0]:.2f} ~ {sheet['Y范围'][1]:.2f}")
            markdown_lines.append(f"**表格数量**: {sheet.get('表格数量', 0)}")
            markdown_lines.append(f"**主图文本数量**: {sheet.get('主图文本数量', 0)}")
            markdown_lines.append("")

            # 表格详情
            tables = sheet.get("表格列表", [])
            if tables:
                markdown_lines.append("### 表格详情")
                markdown_lines.append("")

                for table in tables:
                    table_name = table["表格名称"]
                    table_type = table["表格类型"]

                    markdown_lines.append(f"#### {table_name} - {table_type}")
                    markdown_lines.append("")
                    markdown_lines.append(f"**行数**: {table['行数']}, **列数**: {table['列数']}")
                    markdown_lines.append("")

                    # 生成表格内容
                    cell_grid = table["单元格数据"]
                    if cell_grid:
                        # 表格头部
                        headers = [f"列{i + 1}" for i in range(table['列数'])]
                        markdown_lines.append("| " + " | ".join(headers) + " |")
                        markdown_lines.append("| " + " | ".join(["---"] * len(headers)) + " |")

                        # 表格数据
                        for row in cell_grid:
                            row_data = []
                            for cell in row:
                                content = cell.get("内容", "").strip()
                                # 清理内容用于markdown
                                content = content.replace("|", "\\|").replace("\n", " ")
                                if len(content) > 50:  # 限制单元格内容长度
                                    content = content[:47] + "..."
                                row_data.append(content)
                            markdown_lines.append("| " + " | ".join(row_data) + " |")

                    markdown_lines.append("")

            # 主图文本摘要
            main_texts = sheet.get("主图文本", [])
            if main_texts:
                markdown_lines.append("### 主图文本摘要")
                markdown_lines.append("")

                # 按图层分组显示
                layer_texts = defaultdict(list)
                for text in main_texts:
                    layer = text.get("所在图层", "默认")
                    content = text.get("文本内容", "").strip()
                    if content:
                        layer_texts[layer].append(content)

                for layer, texts in layer_texts.items():
                    if texts:
                        markdown_lines.append(f"**{layer}图层** ({len(texts)}个文本):")
                        for text in texts[:10]:  # 只显示前10个
                            markdown_lines.append(f"- {text}")
                        if len(texts) > 10:
                            markdown_lines.append(f"- ... (还有{len(texts) - 10}个文本)")
                        markdown_lines.append("")

            markdown_lines.append("---")
            markdown_lines.append("")

        return "\n".join(markdown_lines)

    def generate_comprehensive_output(self) -> Dict:
        """生成完整的解析输出"""
        if not self.load_document():
            return {"错误": "无法加载DXF文件"}

        print("开始DXF高级解析...")

        # 第一步：提取所有实体
        print("第一步：提取所有实体...")
        self.extract_all_entities()

        # 第二步：区域划分
        print("第二步：通过空间聚类检测图纸区域...")
        self.detect_drawing_sheets_by_clustering()

        # 第三步：表格检测和重建
        print("第三步：检测和重建表格...")
        self.detect_tables_in_sheets()

        # 生成markdown输出
        print("生成输出...")
        markdown_output = self.generate_markdown_output()

        # 构建最终输出
        output = {
            "文件信息": {
                "文件名": os.path.basename(self.dxf_path),
                "DXF版本": self.doc.dxfversion,
                "总实体数": len(self.all_entities),
                "文本实体数": len(self.text_entities),
                "线实体数": len(self.line_entities)
            },
            "图纸结构": {
                "图纸数量": len(self.drawing_sheets),
                "图纸详情": self.drawing_sheets
            },
            "markdown输出": markdown_output,
            "解析配置": {
                "包含坐标": self.include_coordinates,
                "线段容差": self.line_tolerance,
                "网格容差": self.grid_tolerance,
                "单元格容差": self.cell_tolerance
            }
        }

        return output


def process_dxf_advanced(input_path: Union[str, Path],
                         output_base: Optional[str] = None,
                         include_coordinates: bool = False) -> Dict[str, Any]:
    """
    高级DXF处理函数
    """
    input_path = Path(input_path)

    if not input_path.exists():
        raise FileNotFoundError(f"输入路径不存在: {input_path}")

    # 获取DXF文件列表
    dxf_files = []
    if input_path.is_file():
        if input_path.suffix.lower() == '.dxf':
            dxf_files.append(input_path)
        else:
            raise ValueError(f"输入文件不是DXF格式: {input_path}")
    elif input_path.is_dir():
        dxf_files = list(input_path.rglob('*.dxf')) + list(input_path.rglob('*.DXF'))
        if not dxf_files:
            raise ValueError(f"在目录 {input_path} 中未找到DXF文件")

    # 创建输出目录
    if output_base:
        output_dir = Path(output_base)
    else:
        if input_path.is_file():
            output_dir = input_path.parent / f"{input_path.stem}_advanced_parsed"
        else:
            output_dir = input_path.parent / f"{input_path.name}_advanced_parsed"

    output_dir.mkdir(parents=True, exist_ok=True)

    print(f"找到 {len(dxf_files)} 个DXF文件")
    print(f"输出目录: {output_dir}")

    # 处理统计
    results = {
        "成功": 0,
        "失败": 0,
        "总计": len(dxf_files),
        "失败文件": []
    }

    # 批量处理
    for dxf_file in tqdm(dxf_files, desc="高级解析DXF文件"):
        try:
            print(f"\n处理文件: {dxf_file.name}")

            # 创建解析器
            parser = DXFAdvancedParser(
                str(dxf_file),
                include_coordinates=include_coordinates
            )

            # 执行解析
            result = parser.generate_comprehensive_output()

            if "错误" in result:
                results["失败"] += 1
                results["失败文件"].append(str(dxf_file))
                print(f"✗ 解析失败: {dxf_file.name} - {result['错误']}")
                continue

            # 保存JSON结果
            json_filename = f"{dxf_file.stem}.json"
            json_path = output_dir / json_filename

            safe_result = safe_json_conversion(result)
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(safe_result, f, ensure_ascii=False, indent=2)

            # 保存Markdown结果
            md_filename = f"{dxf_file.stem}.md"
            md_path = output_dir / md_filename

            with open(md_path, 'w', encoding='utf-8') as f:
                f.write(result["markdown输出"])

            results["成功"] += 1
            print(f"✓ 成功解析: {dxf_file.name}")

        except Exception as e:
            results["失败"] += 1
            results["失败文件"].append(str(dxf_file))
            print(f"✗ 解析异常: {dxf_file.name} - {e}")

    # 打印最终统计
    print(f"\n=== 高级解析完成 ===")
    print(f"总文件数: {results['总计']}")
    print(f"成功解析: {results['成功']}")
    print(f"解析失败: {results['失败']}")

    if results["失败文件"]:
        print(f"\n失败文件列表:")
        for failed_file in results["失败文件"]:
            print(f"  - {failed_file}")

    return results


def main():
    """主函数"""
    import sys

    # 示例用法
    if len(sys.argv) > 1:
        input_path = sys.argv[1]
        output_base = sys.argv[2] if len(sys.argv) > 2 else None
        include_coordinates = sys.argv[3].lower() == 'true' if len(sys.argv) > 3 else False
    else:
        # 默认示例
        input_path = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test'
        output_base = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_advanced'
        include_coordinates = False

    try:
        results = process_dxf_advanced(
            input_path,
            output_base,
            include_coordinates=include_coordinates
        )

        if results["总计"] > 0:
            success_rate = (results["成功"] / results["总计"]) * 100
            print(f"\n解析成功率: {success_rate:.1f}%")

        return 0

    except Exception as e:
        print(f"程序执行出错: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
