import ezdxf
import logging
import numpy as np
from collections import defaultdict
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional
import pandas as pd
from sklearn.cluster import DBSCAN
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class Point:
    """坐标点"""
    x: float
    y: float


@dataclass
class Line:
    """线段"""
    start: Point
    end: Point

    @property
    def is_horizontal(self) -> bool:
        """判断是否为水平线"""
        return abs(self.start.y - self.end.y) < 1.0

    @property
    def is_vertical(self) -> bool:
        """判断是否为垂直线"""
        return abs(self.start.x - self.end.x) < 1.0


@dataclass
class Text:
    """文本实体"""
    content: str
    position: Point
    height: float


@dataclass
class TableCell:
    """表格单元格"""
    row: int
    col: int
    content: str
    x_min: float
    x_max: float
    y_min: float
    y_max: float


class DXFParser:
    def __init__(self, filepath: str, output_dir: str = "output"):
        self.filepath = filepath
        self.output_dir = output_dir
        self.doc = ezdxf.readfile(filepath)
        self.msp = self.doc.modelspace()

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 存储解析后的数据
        self.texts: List[Text] = []
        self.lines: List[Line] = []
        self.sheets: Dict[int, Dict] = {}

    def parse(self):
        """主解析流程"""
        logger.info(f"开始解析DXF文件: {self.filepath}")

        # 1. 提取所有实体
        self._extract_entities()

        # 2. 剔除异常点
        valid_texts, valid_lines = self._filter_outliers_advanced()

        # 3. 重置坐标到最小边界框
        reset_texts, reset_lines = self._reset_coordinates(valid_texts, valid_lines)

        # 4. 可视化重置后的整体坐标
        self._visualize_overall_reset(reset_texts, reset_lines)

        # 5. 按X坐标划分图纸
        sheet_regions = self._divide_sheets_by_x(reset_texts, reset_lines)

        # 6. 解析每张图纸并生成可视化
        for sheet_id, region in sheet_regions.items():
            self._parse_sheet_with_visualization(sheet_id, region)

        # 7. 输出结构化结果
        return self._format_output()

    def _extract_entities(self):
        """提取所有实体"""
        logger.info("提取DXF实体...")

        # 提取文本
        for entity in self.msp.query('TEXT'):
            if hasattr(entity.dxf, 'text') and entity.dxf.text.strip():
                self.texts.append(Text(
                    content=entity.dxf.text.strip(),
                    position=Point(entity.dxf.insert[0], entity.dxf.insert[1]),
                    height=getattr(entity.dxf, 'height', 1.0)
                ))

        for entity in self.msp.query('MTEXT'):
            if hasattr(entity, 'text') and entity.text.strip():
                self.texts.append(Text(
                    content=entity.text.strip(),
                    position=Point(entity.dxf.insert[0], entity.dxf.insert[1]),
                    height=getattr(entity.dxf, 'char_height', 1.0)
                ))

        # 提取线段
        for entity in self.msp.query('LINE'):
            self.lines.append(Line(
                start=Point(entity.dxf.start[0], entity.dxf.start[1]),
                end=Point(entity.dxf.end[0], entity.dxf.end[1])
            ))

        logger.info(f"提取到 {len(self.texts)} 个文本实体, {len(self.lines)} 条线段")

    def _filter_outliers_advanced(self) -> Tuple[List[Text], List[Line]]:
        """高级异常点剔除"""
        logger.info("执行高级异常点剔除...")

        if not self.texts:
            return [], self.lines

        # 提取坐标
        coordinates = np.array([[t.position.x, t.position.y] for t in self.texts])

        # 使用DBSCAN聚类找到主要区域
        # 先用较小的eps找到密集区域
        clustering = DBSCAN(eps=20, min_samples=5).fit(coordinates)

        # 找出最大的聚类（主要内容区域）
        labels = clustering.labels_
        unique_labels, counts = np.unique(labels[labels != -1], return_counts=True)

        if len(unique_labels) == 0:
            logger.warning("DBSCAN未找到有效聚类，使用统计方法")
            return self._filter_outliers_statistical()

        # 选择最大的聚类作为主要区域
        main_cluster_label = unique_labels[np.argmax(counts)]
        main_cluster_indices = np.where(labels == main_cluster_label)[0]

        logger.info(
            f"主要聚类包含 {len(main_cluster_indices)} 个点，占总数的 {len(main_cluster_indices) / len(self.texts) * 100:.1f}%")

        # 如果主要聚类太小，扩展包含其他聚类
        if len(main_cluster_indices) < len(self.texts) * 0.6:
            logger.info("主要聚类较小，包含其他聚类")
            valid_indices = np.where(labels != -1)[0]  # 包含所有非噪声点
        else:
            valid_indices = main_cluster_indices

        valid_texts = [self.texts[i] for i in valid_indices]

        # 基于有效文本的边界框过滤线段
        if valid_texts:
            text_x_coords = [t.position.x for t in valid_texts]
            text_y_coords = [t.position.y for t in valid_texts]

            x_min, x_max = min(text_x_coords), max(text_x_coords)
            y_min, y_max = min(text_y_coords), max(text_y_coords)

            # 扩展边界框
            x_margin = (x_max - x_min) * 0.1
            y_margin = (y_max - y_min) * 0.1

            x_min -= x_margin
            x_max += x_margin
            y_min -= y_margin
            y_max += y_margin

            valid_lines = []
            for line in self.lines:
                line_x_coords = [line.start.x, line.end.x]
                line_y_coords = [line.start.y, line.end.y]

                # 检查线段是否在有效区域内
                if (min(line_x_coords) <= x_max and max(line_x_coords) >= x_min and
                        min(line_y_coords) <= y_max and max(line_y_coords) >= y_min):
                    valid_lines.append(line)
        else:
            valid_lines = self.lines

        logger.info(f"剔除异常点后: {len(valid_texts)} 个文本, {len(valid_lines)} 条线段")
        return valid_texts, valid_lines

    def _filter_outliers_statistical(self) -> Tuple[List[Text], List[Line]]:
        """统计方法剔除异常点"""
        coordinates = np.array([[t.position.x, t.position.y] for t in self.texts])

        # 使用IQR方法
        x_coords = coordinates[:, 0]
        y_coords = coordinates[:, 1]

        x_q1, x_q3 = np.percentile(x_coords, [25, 75])
        y_q1, y_q3 = np.percentile(y_coords, [25, 75])

        x_iqr = x_q3 - x_q1
        y_iqr = y_q3 - y_q1

        # 使用较宽松的阈值
        x_lower = x_q1 - 2 * x_iqr
        x_upper = x_q3 + 2 * x_iqr
        y_lower = y_q1 - 2 * y_iqr
        y_upper = y_q3 + 2 * y_iqr

        valid_texts = []
        for text in self.texts:
            if (x_lower <= text.position.x <= x_upper and
                    y_lower <= text.position.y <= y_upper):
                valid_texts.append(text)

        return valid_texts, self.lines

    def _reset_coordinates(self, texts: List[Text], lines: List[Line]) -> Tuple[List[Text], List[Line]]:
        """重置坐标到最小边界框"""
        logger.info("重置坐标到最小边界框...")

        if not texts:
            return texts, lines

        # 计算所有文本的边界框
        text_x_coords = [t.position.x for t in texts]
        text_y_coords = [t.position.y for t in texts]

        x_min = min(text_x_coords)
        y_min = min(text_y_coords)

        logger.info(f"原始坐标范围: X[{x_min:.2f}, {max(text_x_coords):.2f}], Y[{y_min:.2f}, {max(text_y_coords):.2f}]")

        # 重置文本坐标
        reset_texts = []
        for text in texts:
            reset_text = Text(
                content=text.content,
                position=Point(text.position.x - x_min, text.position.y - y_min),
                height=text.height
            )
            reset_texts.append(reset_text)

        # 重置线段坐标
        reset_lines = []
        for line in lines:
            reset_line = Line(
                start=Point(line.start.x - x_min, line.start.y - y_min),
                end=Point(line.end.x - x_min, line.end.y - y_min)
            )
            reset_lines.append(reset_line)

        # 输出重置后的范围
        reset_x_coords = [t.position.x for t in reset_texts]
        reset_y_coords = [t.position.y for t in reset_texts]

        logger.info(
            f"重置后坐标范围: X[{min(reset_x_coords):.2f}, {max(reset_x_coords):.2f}], Y[{min(reset_y_coords):.2f}, {max(reset_y_coords):.2f}]")

        return reset_texts, reset_lines

    def _visualize_overall_reset(self, texts: List[Text], lines: List[Line]):
        """可视化重置后的整体坐标"""
        logger.info("生成重置后的整体坐标可视化图...")

        if not texts:
            logger.warning("没有文本数据可以可视化")
            return

        try:
            fig, ax = plt.subplots(figsize=(20, 15))

            # 绘制所有文本点
            x_coords = [t.position.x for t in texts]
            y_coords = [t.position.y for t in texts]

            # 绘制文本点
            ax.scatter(x_coords, y_coords, c='red', s=10, alpha=0.6, label='文本位置')

            # 绘制部分文本内容（避免过于密集）
            text_sample = texts[::max(1, len(texts) // 100)]  # 最多显示100个文本
            for text in text_sample:
                if len(text.content) > 2:  # 只显示有意义的文本
                    ax.annotate(text.content[:8],
                                (text.position.x, text.position.y),
                                fontsize=6, ha='left', va='bottom',
                                bbox=dict(boxstyle="round,pad=0.1", facecolor='lightblue', alpha=0.7))

            # 绘制线段
            line_sample = lines[::max(1, len(lines) // 500)]  # 最多显示500条线段
            for line in line_sample:
                x_line = [line.start.x, line.end.x]
                y_line = [line.start.y, line.end.y]

                if line.is_horizontal:
                    ax.plot(x_line, y_line, 'g-', linewidth=0.8, alpha=0.6)
                elif line.is_vertical:
                    ax.plot(x_line, y_line, 'b-', linewidth=0.8, alpha=0.6)
                else:
                    ax.plot(x_line, y_line, 'gray', linewidth=0.5, alpha=0.4)
            # 指定一个支持中文的字体，例如苹果自带的
            # 'PingFang HK'
            # 你也可以使用'SimHei'（黑体），但需要确保系统中有这个字体
            plt.rcParams['font.sans-serif'] = ['PingFang HK']
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            ax.set_title(f'重置后的整体坐标图 ({len(texts)} 文本, {len(lines)} 线段)')
            ax.set_xlabel('X坐标 (重置后)')
            ax.set_ylabel('Y坐标 (重置后)')
            ax.legend()

            # 保存图片
            output_path = os.path.join(self.output_dir, 'overall_reset_coordinates.png')
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()

            logger.info(f"整体坐标可视化已保存至: {output_path}")

        except Exception as e:
            logger.error(f"生成整体坐标可视化时出错: {e}")
            plt.close()

    def _divide_sheets_by_x(self, texts: List[Text], lines: List[Line]) -> Dict[int, Dict]:
        """按X坐标划分图纸"""
        logger.info("按X坐标划分图纸...")

        if not texts:
            return {}

        # 获取所有X坐标并排序
        x_coords = sorted([t.position.x for t in texts])

        # 分析X坐标分布，寻找明显的间隙
        x_gaps = []
        gap_threshold = (max(x_coords) - min(x_coords)) * 0.05  # 动态阈值，占总宽度的5%

        logger.info(f"X坐标范围: [{min(x_coords):.2f}, {max(x_coords):.2f}], 间隙阈值: {gap_threshold:.2f}")

        # 计算相邻点的间隙
        for i in range(1, len(x_coords)):
            gap = x_coords[i] - x_coords[i - 1]
            if gap > gap_threshold:
                x_gaps.append((x_coords[i - 1], x_coords[i], gap))

        logger.info(f"发现 {len(x_gaps)} 个显著间隙")
        for gap in x_gaps:
            logger.info(f"间隙: X[{gap[0]:.2f}, {gap[1]:.2f}], 宽度: {gap[2]:.2f}")

        # 根据间隙划分区域
        regions = {}

        if not x_gaps:
            # 没有明显间隙，尝试按密度划分
            logger.info("没有明显间隙，尝试按密度划分")
            regions = self._divide_by_density(texts, lines)
        else:
            # 根据间隙划分
            x_boundaries = [float('-inf')]
            for gap in x_gaps:
                boundary = (gap[0] + gap[1]) / 2
                x_boundaries.append(boundary)
                logger.info(f"设置分界线: X = {boundary:.2f}")
            x_boundaries.append(float('inf'))

            for i in range(len(x_boundaries) - 1):
                sheet_texts = [t for t in texts if x_boundaries[i] < t.position.x < x_boundaries[i + 1]]
                sheet_lines = [l for l in lines if
                               x_boundaries[i] < (l.start.x + l.end.x) / 2 < x_boundaries[i + 1]]

                if sheet_texts:
                    x_min = x_boundaries[i] if x_boundaries[i] != float('-inf') else min(
                        [t.position.x for t in sheet_texts])
                    x_max = x_boundaries[i + 1] if x_boundaries[i + 1] != float('inf') else max(
                        [t.position.x for t in sheet_texts])

                    regions[i + 1] = {
                        'x_min': x_min,
                        'x_max': x_max,
                        'texts': sheet_texts,
                        'lines': sheet_lines
                    }
                    logger.info(
                        f"图纸 {i + 1}: X范围 [{x_min:.2f}, {x_max:.2f}], {len(sheet_texts)} 文本, {len(sheet_lines)} 线段")

        logger.info(f"总共划分出 {len(regions)} 张图纸")
        return regions

    def _divide_by_density(self, texts: List[Text], lines: List[Line]) -> Dict[int, Dict]:
        """按密度划分（当没有明显间隙时）"""
        logger.info("按密度划分图纸...")

        x_coords = [t.position.x for t in texts]
        x_min, x_max = min(x_coords), max(x_coords)

        # 将X轴分成多个区间，计算每个区间的密度
        num_bins = 20
        bin_width = (x_max - x_min) / num_bins
        densities = []

        for i in range(num_bins):
            bin_start = x_min + i * bin_width
            bin_end = x_min + (i + 1) * bin_width

            count = sum(1 for x in x_coords if bin_start <= x < bin_end)
            densities.append(count)

        # 寻找密度的低谷作为分割点
        avg_density = np.mean(densities)
        low_density_threshold = avg_density * 0.3

        split_points = []
        for i in range(1, len(densities) - 1):
            if densities[i] < low_density_threshold:
                split_x = x_min + (i + 0.5) * bin_width
                split_points.append(split_x)

        if not split_points:
            # 强制按中点分割
            split_points = [(x_min + x_max) / 2]
            logger.info("强制按中点分割")

        logger.info(f"按密度找到 {len(split_points)} 个分割点: {split_points}")

        # 根据分割点创建区域
        regions = {}
        boundaries = [float('-inf')] + split_points + [float('inf')]

        for i in range(len(boundaries) - 1):
            sheet_texts = [t for t in texts if boundaries[i] < t.position.x < boundaries[i + 1]]
            sheet_lines = [l for l in lines if
                           boundaries[i] < (l.start.x + l.end.x) / 2 < boundaries[i + 1]]

            if sheet_texts:
                x_min_region = boundaries[i] if boundaries[i] != float('-inf') else min(
                    [t.position.x for t in sheet_texts])
                x_max_region = boundaries[i + 1] if boundaries[i + 1] != float('inf') else max(
                    [t.position.x for t in sheet_texts])

                regions[i + 1] = {
                    'x_min': x_min_region,
                    'x_max': x_max_region,
                    'texts': sheet_texts,
                    'lines': sheet_lines
                }

        return regions

    def _parse_sheet_with_visualization(self, sheet_id: int, region: Dict):
        """解析图纸并生成可视化（使用重置后的坐标）"""
        logger.info(f"解析图纸 {sheet_id} 并生成可视化...")

        # 重置该图纸区域的坐标到(0,0)开始
        reset_region = self._reset_region_coordinates(region)

        # 1. 检测外框线（使用重置后的坐标）
        boundary_boxes = self._detect_boundary_boxes(reset_region['lines'])

        # 2. 可视化外框线（使用重置后的坐标）
        self._visualize_boundary_boxes(reset_region['texts'], reset_region['lines'], boundary_boxes)

        sheet_data = {
            'id': sheet_id,
            'main_drawing': {
                'text_count': len(reset_region['texts']),
                'line_count': len(reset_region['lines'])
            },
            'boundary_boxes': boundary_boxes,
            'tables': [],
            'legends': []
        }

        # 3. 基于外框或传统方法识别表格
        if boundary_boxes:
            # 优先在外框内查找表格
            for bbox in boundary_boxes:
                bbox_texts = [t for t in reset_region['texts'] if
                              bbox['x_min'] <= t.position.x <= bbox['x_max'] and
                              bbox['y_min'] <= t.position.y <= bbox['y_max']]
                bbox_lines = [l for l in reset_region['lines'] if
                              bbox['x_min'] <= (l.start.x + l.end.x) / 2 <= bbox['x_max'] and
                              bbox['y_min'] <= (l.start.y + l.end.y) / 2 <= bbox['y_max']]

                bbox_region = {
                    'texts': bbox_texts,
                    'lines': bbox_lines,
                    **bbox
                }

                tables = self._identify_tables(bbox_region)
                for i, table_region in enumerate(tables):
                    table_data = self._parse_table_advanced(table_region)
                    if table_data:
                        table_data['id'] = len(sheet_data['tables']) + 1
                        table_data['boundary_box'] = bbox
                        sheet_data['tables'].append(table_data)
        else:
            # 传统方法
            tables = self._identify_tables(reset_region)
            for i, table_region in enumerate(tables):
                table_data = self._parse_table_advanced(table_region)
                if table_data:
                    table_data['id'] = i + 1
                    sheet_data['tables'].append(table_data)

        # 4. 识别图例
        legends = self._identify_legends(reset_region)
        for i, legend_region in enumerate(legends):
            legend_data = self._parse_legend(legend_region)
            if legend_data:
                legend_data['id'] = i + 1
                sheet_data['legends'].append(legend_data)

        # 5. 生成可视化（使用重置后的坐标）
        self._create_sheet_visualization_reset(sheet_id, reset_region, sheet_data)

        self.sheets[sheet_id] = sheet_data

    def _create_sheet_visualization_reset(self, sheet_id: int, region: Dict, sheet_data: Dict):
        """创建图纸的详细可视化（使用重置后的坐标，保留文字）"""
        logger.info(f"创建图纸 {sheet_id} 的可视化（重置坐标）...")

        try:
            fig, axes = plt.subplots(2, 2, figsize=(24, 18))
            fig.suptitle(f'图纸 {sheet_id} 详细分析（坐标已重置）', fontsize=16, fontweight='bold')

            # 1. 整体图纸视图（保留所有文字）
            ax1 = axes[0, 0]
            self._plot_sheet_overview_with_text(ax1, region, f'图纸 {sheet_id} 整体视图')

            # 2. 文本分布密度图
            ax2 = axes[0, 1]
            self._plot_text_distribution_enhanced(ax2, region['texts'], f'图纸 {sheet_id} 文本分布')

            # 3. 线段网格分析
            ax3 = axes[1, 0]
            self._plot_line_grid_analysis(ax3, region['lines'], f'图纸 {sheet_id} 网格线分析')

            # 4. 表格和图例区域标注
            ax4 = axes[1, 1]
            self._plot_regions_with_content(ax4, region, sheet_data, f'图纸 {sheet_id} 内容区域')

            plt.tight_layout()

            # 保存图片
            output_path = os.path.join(self.output_dir, f'sheet_{sheet_id}_detailed_analysis.png')
            plt.savefig(output_path, dpi=200, bbox_inches='tight')
            plt.close()

            logger.info(f"图纸 {sheet_id} 详细可视化已保存至: {output_path}")

        except Exception as e:
            logger.error(f"创建图纸 {sheet_id} 可视化时出错: {e}")
            plt.close()

    def _plot_sheet_overview_with_text(self, ax, region: Dict, title: str):
        """绘制图纸整体视图，保留所有文字"""
        texts = region['texts']
        lines = region['lines']

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 绘制所有文本（完整显示）
        if texts:
            for text in texts:
                # 根据文本长度和重要性调整显示
                font_size = 6
                alpha = 0.8

                # 重要文本用不同颜色标注
                if any(keyword in text.content for keyword in ['工程号', '表格', '图例', '说明', '明细']):
                    bbox_props = dict(boxstyle="round,pad=0.2", facecolor='yellow', alpha=0.9)
                    font_size = 8
                elif len(text.content) > 5:
                    bbox_props = dict(boxstyle="round,pad=0.1", facecolor='lightblue', alpha=0.7)
                    font_size = 7
                else:
                    bbox_props = dict(boxstyle="round,pad=0.05", facecolor='lightgray', alpha=0.5)

                ax.annotate(text.content,
                            (text.position.x, text.position.y),
                            fontsize=font_size, ha='left', va='bottom',
                            bbox=bbox_props, alpha=alpha)

        # 绘制线段（区分类型）
        if lines:
            h_lines = [l for l in lines if l.is_horizontal]
            v_lines = [l for l in lines if l.is_vertical]
            other_lines = [l for l in lines if not l.is_horizontal and not l.is_vertical]

            # 水平线 - 绿色
            for line in h_lines:
                ax.plot([line.start.x, line.end.x], [line.start.y, line.end.y],
                        'g-', linewidth=1.2, alpha=0.7, label='水平线' if line == h_lines[0] else "")

            # 垂直线 - 蓝色
            for line in v_lines:
                ax.plot([line.start.x, line.end.x], [line.start.y, line.end.y],
                        'b-', linewidth=1.2, alpha=0.7, label='垂直线' if line == v_lines[0] else "")

            # 其他线段 - 灰色
            for line in other_lines[:100]:  # 限制数量避免过于密集
                ax.plot([line.start.x, line.end.x], [line.start.y, line.end.y],
                        'gray', linewidth=0.6, alpha=0.4, label='其他线段' if line == other_lines[0] else "")

        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_title(f'{title}\n文本: {len(texts)}, 线段: {len(lines)}', fontsize=12, fontweight='bold')
        ax.set_xlabel('X坐标 (重置后)', fontsize=10)
        ax.set_ylabel('Y坐标 (重置后)', fontsize=10)

        # 添加图例
        handles, labels = ax.get_legend_handles_labels()
        if handles:
            ax.legend(handles[:3], labels[:3], loc='upper right', fontsize=8)

    def _plot_text_distribution_enhanced(self, ax, texts: List[Text], title: str):
        """增强的文本分布图"""
        if not texts:
            ax.text(0.5, 0.5, '无文本数据', ha='center', va='center',
                    transform=ax.transAxes, fontsize=14)
            ax.set_title(title)
            return

        x_coords = [t.position.x for t in texts]
        y_coords = [t.position.y for t in texts]

        # 创建热力图
        hb = ax.hexbin(x_coords, y_coords, gridsize=15, cmap='Blues', alpha=0.7)
        plt.colorbar(hb, ax=ax, label='文本密度')

        # 标注关键文本
        keywords = ['工程号', '表格', '图例', '说明', '明细表', '材料表', '设备表']
        colors = ['red', 'blue', 'green', 'purple', 'orange', 'brown', 'pink']

        for i, keyword in enumerate(keywords):
            keyword_texts = [t for t in texts if keyword in t.content]
            if keyword_texts:
                kw_x = [t.position.x for t in keyword_texts]
                kw_y = [t.position.y for t in keyword_texts]
                color = colors[i % len(colors)]

                ax.scatter(kw_x, kw_y, c=color, s=50, alpha=0.8,
                           label=f'{keyword}({len(keyword_texts)})', marker='o', edgecolors='black')

                # 标注文本内容
                for text in keyword_texts:
                    ax.annotate(text.content[:15],
                                (text.position.x, text.position.y),
                                xytext=(5, 5), textcoords='offset points',
                                fontsize=8, ha='left', va='bottom',
                                bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.8),
                                arrowprops=dict(arrowstyle='->', color=color))

        ax.set_title(f'{title}\n总文本数: {len(texts)}', fontsize=12, fontweight='bold')
        ax.set_xlabel('X坐标', fontsize=10)
        ax.set_ylabel('Y坐标', fontsize=10)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)

    def _plot_line_grid_analysis(self, ax, lines: List[Line], title: str):
        """网格线分析图"""
        if not lines:
            ax.text(0.5, 0.5, '无线段数据', ha='center', va='center',
                    transform=ax.transAxes, fontsize=14)
            ax.set_title(title)
            return

        h_lines = [l for l in lines if l.is_horizontal]
        v_lines = [l for l in lines if l.is_vertical]
        other_lines = [l for l in lines if not l.is_horizontal and not l.is_vertical]

        # 绘制水平线
        for line in h_lines:
            ax.plot([line.start.x, line.end.x], [line.start.y, line.end.y],
                    'g-', linewidth=1.5, alpha=0.8)

        # 绘制垂直线
        for line in v_lines:
            ax.plot([line.start.x, line.end.x], [line.start.y, line.end.y],
                    'b-', linewidth=1.5, alpha=0.8)

        # 绘制其他线段
        for line in other_lines:
            ax.plot([line.start.x, line.end.x], [line.start.y, line.end.y],
                    'gray', linewidth=0.8, alpha=0.5)

        # 分析网格密度
        if h_lines and v_lines:
            # 找到网格交点
            intersections = []
            for h_line in h_lines:
                for v_line in v_lines:
                    # 简化的交点检测
                    if (min(h_line.start.x, h_line.end.x) <= v_line.start.x <= max(h_line.start.x, h_line.end.x) and
                            min(v_line.start.y, v_line.end.y) <= h_line.start.y <= max(v_line.start.y, v_line.end.y)):
                        intersections.append((v_line.start.x, h_line.start.y))

            # 标注交点
            if intersections:
                int_x, int_y = zip(*intersections)
                ax.scatter(int_x, int_y, c='red', s=20, alpha=0.6, marker='+', label=f'网格交点({len(intersections)})')

        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_title(f'{title}\n水平线: {len(h_lines)}, 垂直线: {len(v_lines)}, 其他: {len(other_lines)}',
                     fontsize=12, fontweight='bold')
        ax.set_xlabel('X坐标', fontsize=10)
        ax.set_ylabel('Y坐标', fontsize=10)
        ax.legend(fontsize=8)

    def _plot_regions_with_content(self, ax, region: Dict, sheet_data: Dict, title: str):
        """绘制内容区域，显示表格和图例的具体内容"""
        texts = region['texts']

        # 绘制所有文本作为背景
        if texts:
            x_coords = [t.position.x for t in texts]
            y_coords = [t.position.y for t in texts]
            ax.scatter(x_coords, y_coords, c='lightgray', s=8, alpha=0.4, label='所有文本')

        # 标注表格区域和内容
        colors = ['red', 'blue', 'green', 'purple', 'orange', 'brown', 'pink']

        # 显示表格信息
        for i, table in enumerate(sheet_data['tables']):
            color = colors[i % len(colors)]

            # 在图上标注表格信息
            info_text = f'表格 {table["id"]}: {table["rows"]}行×{table["cols"]}列\n填充: {table["filled_cells"]}个单元格'
            if table.get('merged_cells', 0) > 0:
                info_text += f'\n合并单元格: {table["merged_cells"]}个'

            ax.text(0.02, 0.98 - i * 0.15, info_text,
                    transform=ax.transAxes, fontsize=10,
                    bbox=dict(boxstyle="round,pad=0.5", facecolor=color, alpha=0.8),
                    verticalalignment='top', color='white', fontweight='bold')

            # 如果有边界框信息，绘制边界框
            if 'boundary_box' in table:
                bbox = table['boundary_box']
                rect = patches.Rectangle((bbox['x_min'], bbox['y_min']),
                                         bbox['width'], bbox['height'],
                                         linewidth=2, edgecolor=color,
                                         facecolor='none', linestyle='--', alpha=0.8)
                ax.add_patch(rect)

        # 显示图例信息
        legend_start_y = 0.98 - len(sheet_data['tables']) * 0.15
        for i, legend in enumerate(sheet_data['legends']):
            color = colors[(i + len(sheet_data['tables'])) % len(colors)]

            info_text = f'图例 {legend["id"]}: {len(legend["items"])}项'
            # 显示前几项内容
            if legend["items"]:
                preview_items = legend["items"][:3]
                info_text += f'\n内容: {", ".join(preview_items)}'
                if len(legend["items"]) > 3:
                    info_text += f'...(共{len(legend["items"])}项)'

            ax.text(0.52, legend_start_y - i * 0.15, info_text,
                    transform=ax.transAxes, fontsize=10,
                    bbox=dict(boxstyle="round,pad=0.5", facecolor=color, alpha=0.8),
                    verticalalignment='top', color='white', fontweight='bold')

        # 显示外框信息
        if 'boundary_boxes' in sheet_data and sheet_data['boundary_boxes']:
            bbox_info = f"检测到 {len(sheet_data['boundary_boxes'])} 个外框"
            ax.text(0.02, 0.02, bbox_info, transform=ax.transAxes, fontsize=12,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.9),
                    fontweight='bold')

        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_title(title, fontsize=12, fontweight='bold')
        ax.set_xlabel('X坐标', fontsize=10)
        ax.set_ylabel('Y坐标', fontsize=10)

        if texts:
            ax.legend(fontsize=8)

    def _reset_region_coordinates(self, region: Dict) -> Dict:
        """重置单个图纸区域的坐标"""
        texts = region['texts']
        lines = region['lines']

        if not texts:
            return region

        # 计算该区域的边界
        text_x_coords = [t.position.x for t in texts]
        text_y_coords = [t.position.y for t in texts]

        x_min = min(text_x_coords)
        y_min = min(text_y_coords)

        logger.info(f"图纸区域坐标重置: 原点从({x_min:.2f}, {y_min:.2f})移至(0, 0)")

        # 重置文本坐标
        reset_texts = []
        for text in texts:
            reset_text = Text(
                content=text.content,
                position=Point(text.position.x - x_min, text.position.y - y_min),
                height=text.height
            )
            reset_texts.append(reset_text)

        # 重置线段坐标
        reset_lines = []
        for line in lines:
            reset_line = Line(
                start=Point(line.start.x - x_min, line.start.y - y_min),
                end=Point(line.end.x - x_min, line.end.y - y_min)
            )
            reset_lines.append(reset_line)

        return {
            'texts': reset_texts,
            'lines': reset_lines,
            'x_min': 0,
            'x_max': region['x_max'] - x_min,
            'y_min': 0,
            'y_max': max(text_y_coords) - y_min
        }
    def _detect_boundary_boxes(self, lines: List[Line]) -> List[Dict]:
        """检测图纸中的外框线（大矩形边界框）"""
        logger.info("检测外框线边界框...")

        # 分离水平线和垂直线
        h_lines = [l for l in lines if l.is_horizontal]
        v_lines = [l for l in lines if l.is_vertical]

        logger.info(f"可用线段: {len(h_lines)} 条水平线, {len(v_lines)} 条垂直线")

        # 找到可能的矩形外框
        boundary_boxes = []

        # 对于每条水平线，寻找能与之形成矩形的其他线段
        for i, h_line1 in enumerate(h_lines):
            for j, h_line2 in enumerate(h_lines[i + 1:], i + 1):
                # 检查两条水平线是否平行且有一定距离
                y_diff = abs(h_line1.start.y - h_line2.start.y)
                if y_diff < 50:  # 太近的线不考虑
                    continue

                # 寻找连接这两条水平线的垂直线
                for v_line1 in v_lines:
                    for v_line2 in v_lines:
                        if v_line1 == v_line2:
                            continue

                        # 检查是否能形成矩形
                        rect = self._check_rectangle_formation(h_line1, h_line2, v_line1, v_line2)
                        if rect:
                            boundary_boxes.append(rect)

        # 去重并按面积排序
        boundary_boxes = self._deduplicate_rectangles(boundary_boxes)
        boundary_boxes.sort(key=lambda x: x['area'], reverse=True)

        logger.info(f"检测到 {len(boundary_boxes)} 个可能的外框")

        return boundary_boxes

    def _check_rectangle_formation(self, h_line1: Line, h_line2: Line, v_line1: Line, v_line2: Line) -> Optional[Dict]:
        """检查四条线是否能形成矩形"""
        tolerance = 5.0  # 坐标容差

        # 获取线段端点
        h1_x1, h1_x2 = min(h_line1.start.x, h_line1.end.x), max(h_line1.start.x, h_line1.end.x)
        h2_x1, h2_x2 = min(h_line2.start.x, h_line2.end.x), max(h_line2.start.x, h_line2.end.x)

        v1_y1, v1_y2 = min(v_line1.start.y, v_line1.end.y), max(v_line1.start.y, v_line1.end.y)
        v2_y1, v2_y2 = min(v_line2.start.y, v_line2.end.y), max(v_line2.start.y, v_line2.end.y)

        h1_y = h_line1.start.y
        h2_y = h_line2.start.y
        v1_x = v_line1.start.x
        v2_x = v_line2.start.x

        # 检查矩形的四个角是否对齐
        # 水平线的X范围应该与垂直线的X坐标匹配
        # 垂直线的Y范围应该与水平线的Y坐标匹配

        if (abs(h1_x1 - v1_x) < tolerance and abs(h1_x2 - v2_x) < tolerance and
                abs(h2_x1 - v1_x) < tolerance and abs(h2_x2 - v2_x) < tolerance and
                abs(v1_y1 - min(h1_y, h2_y)) < tolerance and abs(v1_y2 - max(h1_y, h2_y)) < tolerance and
                abs(v2_y1 - min(h1_y, h2_y)) < tolerance and abs(v2_y2 - max(h1_y, h2_y)) < tolerance):
            # 计算矩形参数
            x_min = min(v1_x, v2_x)
            x_max = max(v1_x, v2_x)
            y_min = min(h1_y, h2_y)
            y_max = max(h1_y, h2_y)

            width = x_max - x_min
            height = y_max - y_min
            area = width * height

            return {
                'x_min': x_min,
                'x_max': x_max,
                'y_min': y_min,
                'y_max': y_max,
                'width': width,
                'height': height,
                'area': area,
                'lines': [h_line1, h_line2, v_line1, v_line2]
            }

        return None

    def _deduplicate_rectangles(self, rectangles: List[Dict]) -> List[Dict]:
        """去除重复的矩形"""
        if not rectangles:
            return []

        unique_rects = []
        tolerance = 10.0

        for rect in rectangles:
            is_duplicate = False
            for existing in unique_rects:
                if (abs(rect['x_min'] - existing['x_min']) < tolerance and
                        abs(rect['x_max'] - existing['x_max']) < tolerance and
                        abs(rect['y_min'] - existing['y_min']) < tolerance and
                        abs(rect['y_max'] - existing['y_max']) < tolerance):
                    is_duplicate = True
                    break

            if not is_duplicate:
                unique_rects.append(rect)

        return unique_rects

    def _visualize_boundary_boxes(self, texts: List[Text], lines: List[Line], boundary_boxes: List[Dict]):
        """可视化检测到的外框线（使用重置后的坐标，保留文字）"""
        logger.info("生成外框线检测可视化图（重置坐标）...")

        if not boundary_boxes:
            logger.warning("没有检测到外框线")
            return

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        try:
            # 为每个外框创建单独的图
            for i, bbox in enumerate(boundary_boxes[:5]):  # 最多显示前5个
                fig, ax = plt.subplots(figsize=(18, 14))

                # 绘制该外框区域内的所有文本（完整显示）
                region_texts = [t for t in texts if
                                bbox['x_min'] <= t.position.x <= bbox['x_max'] and
                                bbox['y_min'] <= t.position.y <= bbox['y_max']]

                # 绘制所有文本内容
                for text in region_texts:
                    font_size = 8
                    if any(keyword in text.content for keyword in ['工程号', '表格', '图例', '说明']):
                        bbox_props = dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.9)
                        font_size = 10
                    elif len(text.content) > 3:
                        bbox_props = dict(boxstyle="round,pad=0.2", facecolor='lightblue', alpha=0.8)
                        font_size = 9
                    else:
                        bbox_props = dict(boxstyle="round,pad=0.1", facecolor='lightgray', alpha=0.6)

                    ax.text(text.position.x, text.position.y, text.content,
                            fontsize=font_size, ha='left', va='bottom',
                            bbox=bbox_props)

                # 绘制外框线（高亮显示）
                for line in bbox['lines']:
                    ax.plot([line.start.x, line.end.x], [line.start.y, line.end.y],
                            'red', linewidth=4, alpha=0.9, label='外框线')

                # 绘制其他相关线段
                other_lines = [l for l in lines if l not in bbox['lines']]
                for line in other_lines:
                    if (bbox['x_min'] <= (line.start.x + line.end.x) / 2 <= bbox['x_max'] and
                            bbox['y_min'] <= (line.start.y + line.end.y) / 2 <= bbox['y_max']):

                        if line.is_horizontal:
                            ax.plot([line.start.x, line.end.x], [line.start.y, line.end.y],
                                    'green', linewidth=1.5, alpha=0.7,
                                    label='水平线' if 'green' not in [l.get_color() for l in ax.lines] else "")
                        elif line.is_vertical:
                            ax.plot([line.start.x, line.end.x], [line.start.y, line.end.y],
                                    'blue', linewidth=1.5, alpha=0.7,
                                    label='垂直线' if 'blue' not in [l.get_color() for l in ax.lines] else "")
                        else:
                            ax.plot([line.start.x, line.end.x], [line.start.y, line.end.y],
                                    'gray', linewidth=0.8, alpha=0.5)

                # 绘制外框矩形边界
                rect = patches.Rectangle((bbox['x_min'], bbox['y_min']), bbox['width'], bbox['height'],
                                         linewidth=3, edgecolor='red', facecolor='none',
                                         linestyle='--', alpha=0.8)
                ax.add_patch(rect)

                ax.set_aspect('equal')
                ax.grid(True, alpha=0.3)
                ax.set_title(f'外框 {i + 1} (坐标已重置)\n'
                             f'面积: {bbox["area"]:.0f}, 尺寸: {bbox["width"]:.1f}×{bbox["height"]:.1f}\n'
                             f'文本数量: {len(region_texts)}',
                             fontsize=14, fontweight='bold')
                ax.set_xlabel('X坐标 (重置后)', fontsize=12)
                ax.set_ylabel('Y坐标 (重置后)', fontsize=12)

                # 添加图例
                handles, labels = ax.get_legend_handles_labels()
                if handles:
                    ax.legend(handles, labels, loc='upper right', fontsize=10)

                # 保存图片
                output_path = os.path.join(self.output_dir, f'boundary_box_{i + 1}_reset.png')
                plt.savefig(output_path, dpi=200, bbox_inches='tight')
                plt.close()

                logger.info(f"外框 {i + 1} 可视化（重置坐标）已保存至: {output_path}")

        except Exception as e:
            logger.error(f"生成外框线可视化时出错: {e}")
            plt.close()

    def _parse_table_advanced(self, table_region: Dict) -> Optional[Dict]:
        """高级表格解析 - 基于网格线重建"""
        if not table_region:
            return None

        logger.info("执行高级表格解析...")

        # 1. 提取并分析网格线
        h_lines = [l for l in table_region['lines'] if l.is_horizontal]
        v_lines = [l for l in table_region['lines'] if l.is_vertical]

        logger.info(f"表格网格线: {len(h_lines)} 条水平线, {len(v_lines)} 条垂直线")

        if len(h_lines) < 2 or len(v_lines) < 2:
            logger.warning("网格线数量不足，无法构成表格")
            return None

        # 2. 提取并清理坐标
        row_boundaries = self._extract_row_boundaries(h_lines)
        col_boundaries = self._extract_col_boundaries(v_lines)

        logger.info(f"表格边界: {len(row_boundaries)} 行边界, {len(col_boundaries)} 列边界")

        if len(row_boundaries) < 2 or len(col_boundaries) < 2:
            return None

        # 3. 创建单元格网格
        cells = self._create_cell_grid(row_boundaries, col_boundaries)

        # 4. 将文本分配到单元格
        filled_cells = self._assign_texts_to_cells(table_region['texts'], cells)

        # 5. 检测并处理合并单元格
        merged_cells = self._detect_merged_cells(cells, h_lines, v_lines)

        # 6. 生成表格数据结构
        table_data = self._build_table_structure(cells, merged_cells)

        return {
            'type': 'table',
            'data': table_data,
            'rows': len(row_boundaries) - 1,
            'cols': len(col_boundaries) - 1,
            'filled_cells': filled_cells,
            'merged_cells': len(merged_cells),
            'cell_details': cells
        }

    def _extract_row_boundaries(self, h_lines: List[Line]) -> List[float]:
        """提取行边界Y坐标"""
        y_coords = []
        for line in h_lines:
            y_coords.extend([line.start.y, line.end.y])

        # 去重并排序
        unique_y = sorted(list(set(y_coords)))

        # 合并过于接近的坐标
        merged_y = []
        threshold = 3.0

        for y in unique_y:
            if not merged_y or y - merged_y[-1] > threshold:
                merged_y.append(y)

        return merged_y

    def _extract_col_boundaries(self, v_lines: List[Line]) -> List[float]:
        """提取列边界X坐标"""
        x_coords = []
        for line in v_lines:
            x_coords.extend([line.start.x, line.end.x])

        # 去重并排序
        unique_x = sorted(list(set(x_coords)))

        # 合并过于接近的坐标
        merged_x = []
        threshold = 3.0

        for x in unique_x:
            if not merged_x or x - merged_x[-1] > threshold:
                merged_x.append(x)

        return merged_x

    def _create_cell_grid(self, row_boundaries: List[float], col_boundaries: List[float]) -> List[List[Dict]]:
        """创建单元格网格"""
        rows = len(row_boundaries) - 1
        cols = len(col_boundaries) - 1

        cells = []
        for i in range(rows):
            row = []
            for j in range(cols):
                cell = {
                    'row': i,
                    'col': j,
                    'x_min': col_boundaries[j],
                    'x_max': col_boundaries[j + 1],
                    'y_min': row_boundaries[i],
                    'y_max': row_boundaries[i + 1],
                    'content': '',
                    'texts': [],
                    'is_merged': False,
                    'merge_info': None
                }
                row.append(cell)
            cells.append(row)

        return cells

    def _assign_texts_to_cells(self, texts: List[Text], cells: List[List[Dict]]) -> int:
        """将文本分配到对应单元格"""
        filled_count = 0

        for text in texts:
            assigned = False

            # 遍历所有单元格，找到包含该文本的单元格
            for row in cells:
                for cell in row:
                    if (cell['x_min'] <= text.position.x <= cell['x_max'] and
                            cell['y_min'] <= text.position.y <= cell['y_max']):

                        cell['texts'].append(text)
                        if cell['content']:
                            cell['content'] += ' ' + text.content
                        else:
                            cell['content'] = text.content
                            filled_count += 1

                        assigned = True
                        break

                if assigned:
                    break

            if not assigned:
                logger.debug(
                    f"文本 '{text.content}' 在 ({text.position.x:.2f}, {text.position.y:.2f}) 未找到对应单元格")

        return filled_count

    def _detect_merged_cells(self, cells: List[List[Dict]], h_lines: List[Line], v_lines: List[Line]) -> List[Dict]:
        """检测合并单元格"""
        merged_cells = []
        rows = len(cells)
        cols = len(cells[0]) if cells else 0

        # 检查水平合并（同一行中缺少垂直分隔线）
        for i in range(rows):
            j = 0
            while j < cols - 1:
                # 检查单元格 (i, j) 和 (i, j+1) 之间是否缺少垂直线
                cell1 = cells[i][j]
                cell2 = cells[i][j + 1]

                # 查找应该存在的垂直分隔线
                expected_x = cell1['x_max']  # 应该等于 cell2['x_min']

                has_separator = False
                for v_line in v_lines:
                    if (abs(v_line.start.x - expected_x) < 2.0 and
                            cell1['y_min'] <= v_line.start.y <= cell1['y_max'] and
                            cell1['y_min'] <= v_line.end.y <= cell1['y_max']):
                        has_separator = True
                        break

                if not has_separator:
                    # 发现水平合并
                    merge_end = j + 1
                    # 继续查找合并的范围
                    while merge_end < cols - 1:
                        next_cell = cells[i][merge_end + 1]
                        next_expected_x = cells[i][merge_end]['x_max']

                        next_has_separator = False
                        for v_line in v_lines:
                            if (abs(v_line.start.x - next_expected_x) < 2.0 and
                                    cell1['y_min'] <= v_line.start.y <= cell1['y_max']):
                                next_has_separator = True
                                break

                        if next_has_separator:
                            break
                        merge_end += 1

                    merged_cells.append({
                        'type': 'horizontal',
                        'start_row': i,
                        'start_col': j,
                        'end_row': i,
                        'end_col': merge_end,
                        'span_rows': 1,
                        'span_cols': merge_end - j + 1
                    })

                    j = merge_end + 1
                else:
                    j += 1

        # 检查垂直合并（同一列中缺少水平分隔线）
        for j in range(cols):
            i = 0
            while i < rows - 1:
                cell1 = cells[i][j]
                cell2 = cells[i + 1][j]

                expected_y = cell1['y_min']  # 应该等于 cell2['y_max']

                has_separator = False
                for h_line in h_lines:
                    if (abs(h_line.start.y - expected_y) < 2.0 and
                            cell1['x_min'] <= h_line.start.x <= cell1['x_max'] and
                            cell1['x_min'] <= h_line.end.x <= cell1['x_max']):
                        has_separator = True
                        break

                if not has_separator:
                    # 发现垂直合并
                    merge_end = i + 1
                    while merge_end < rows - 1:
                        next_expected_y = cells[merge_end][j]['y_min']

                        next_has_separator = False
                        for h_line in h_lines:
                            if (abs(h_line.start.y - next_expected_y) < 2.0 and
                                    cell1['x_min'] <= h_line.start.x <= cell1['x_max']):
                                next_has_separator = True
                                break

                        if next_has_separator:
                            break
                        merge_end += 1

                    merged_cells.append({
                        'type': 'vertical',
                        'start_row': i,
                        'start_col': j,
                        'end_row': merge_end,
                        'end_col': j,
                        'span_rows': merge_end - i + 1,
                        'span_cols': 1
                    })

                    i = merge_end + 1
                else:
                    i += 1

        logger.info(f"检测到 {len(merged_cells)} 个合并单元格")
        return merged_cells

    def _build_table_structure(self, cells: List[List[Dict]], merged_cells: List[Dict]) -> List[List[str]]:
        """构建最终的表格数据结构"""
        if not cells:
            return []

        rows = len(cells)
        cols = len(cells[0])

        # 初始化表格数据
        table_data = [['' for _ in range(cols)] for _ in range(rows)]

        # 填充单元格内容
        for i in range(rows):
            for j in range(cols):
                cell = cells[i][j]
                table_data[i][j] = cell['content']

        # 处理合并单元格
        for merge in merged_cells:
            # 将合并单元格的内容集中到起始单元格
            merged_content = []

            for i in range(merge['start_row'], merge['end_row'] + 1):
                for j in range(merge['start_col'], merge['end_col'] + 1):
                    if table_data[i][j]:
                        merged_content.append(table_data[i][j])

                    # 清空非起始单元格
                    if i != merge['start_row'] or j != merge['start_col']:
                        table_data[i][j] = ''

            # 将合并的内容放入起始单元格
            if merged_content:
                table_data[merge['start_row']][merge['start_col']] = ' '.join(merged_content)

        return table_data
    def _create_sheet_visualization(self, sheet_id: int, region: Dict, sheet_data: Dict):
        """创建图纸的详细可视化"""
        logger.info(f"创建图纸 {sheet_id} 的可视化...")

        try:
            fig, axes = plt.subplots(2, 2, figsize=(20, 16))
            fig.suptitle(f'图纸 {sheet_id} 详细分析', fontsize=16)

            # 1. 整体图纸视图
            ax1 = axes[0, 0]
            self._plot_sheet_overview(ax1, region, f'图纸 {sheet_id} 整体视图')

            # 2. 文本分布图
            ax2 = axes[0, 1]
            self._plot_text_distribution(ax2, region['texts'], f'图纸 {sheet_id} 文本分布')

            # 3. 线段分布图
            ax3 = axes[1, 0]
            self._plot_line_distribution(ax3, region['lines'], f'图纸 {sheet_id} 线段分布')

            # 4. 表格和图例标注
            ax4 = axes[1, 1]
            self._plot_table_legend_regions(ax4, region, sheet_data, f'图纸 {sheet_id} 表格图例区域')

            plt.tight_layout()

            # 保存图片
            output_path = os.path.join(self.output_dir, f'sheet_{sheet_id}_detailed_analysis.png')
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()

            logger.info(f"图纸 {sheet_id} 详细可视化已保存至: {output_path}")

        except Exception as e:
            logger.error(f"创建图纸 {sheet_id} 可视化时出错: {e}")
            plt.close()

    def _plot_sheet_overview(self, ax, region: Dict, title: str):
        """绘制图纸整体视图"""
        # 绘制文本点
        texts = region['texts']
        if texts:
            x_coords = [t.position.x for t in texts]
            y_coords = [t.position.y for t in texts]
            ax.scatter(x_coords, y_coords, c='red', s=8, alpha=0.6, label=f'文本({len(texts)})')

        # 绘制线段
        lines = region['lines']
        h_lines = [l for l in lines if l.is_horizontal]
        v_lines = [l for l in lines if l.is_vertical]
        other_lines = [l for l in lines if not l.is_horizontal and not l.is_vertical]

        for line in h_lines[:100]:  # 限制显示数量
            ax.plot([line.start.x, line.end.x], [line.start.y, line.end.y], 'g-', linewidth=1, alpha=0.7)

        for line in v_lines[:100]:
            ax.plot([line.start.x, line.end.x], [line.start.y, line.end.y], 'b-', linewidth=1, alpha=0.7)

        for line in other_lines[:50]:
            ax.plot([line.start.x, line.end.x], [line.start.y, line.end.y], 'gray', linewidth=0.5, alpha=0.5)

        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_title(title)
        ax.legend()

    def _plot_text_distribution(self, ax, texts: List[Text], title: str):
        """绘制文本分布图"""
        if not texts:
            ax.text(0.5, 0.5, '无文本数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return

        x_coords = [t.position.x for t in texts]
        y_coords = [t.position.y for t in texts]

        # 创建热力图
        ax.hist2d(x_coords, y_coords, bins=20, alpha=0.7, cmap='Blues')

        # 标注一些重要文本
        important_texts = [t for t in texts if
                           any(keyword in t.content for keyword in ['工程号', '表格', '图例', '说明'])]
        for text in important_texts:
            ax.annotate(text.content[:10],
                        (text.position.x, text.position.y),
                        fontsize=8, ha='left', va='bottom',
                        bbox=dict(boxstyle="round,pad=0.2", facecolor='yellow', alpha=0.8))

        ax.set_title(title)
        ax.set_xlabel('X坐标')
        ax.set_ylabel('Y坐标')

    def _plot_line_distribution(self, ax, lines: List[Line], title: str):
        """绘制线段分布图"""
        if not lines:
            ax.text(0.5, 0.5, '无线段数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return

        h_lines = [l for l in lines if l.is_horizontal]
        v_lines = [l for l in lines if l.is_vertical]

        # 绘制水平线
        for line in h_lines:
            ax.plot([line.start.x, line.end.x], [line.start.y, line.end.y], 'g-', linewidth=0.8, alpha=0.6)

        # 绘制垂直线
        for line in v_lines:
            ax.plot([line.start.x, line.end.x], [line.start.y, line.end.y], 'b-', linewidth=0.8, alpha=0.6)

        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_title(f'{title}\n水平线: {len(h_lines)}, 垂直线: {len(v_lines)}')
        ax.legend(['水平线', '垂直线'])

    def _plot_table_legend_regions(self, ax, region: Dict, sheet_data: Dict, title: str):
        """绘制表格和图例区域"""
        # 绘制所有文本作为背景
        texts = region['texts']
        if texts:
            x_coords = [t.position.x for t in texts]
            y_coords = [t.position.y for t in texts]
            ax.scatter(x_coords, y_coords, c='lightgray', s=5, alpha=0.5)

        # 标注表格区域
        colors = ['red', 'blue', 'green', 'purple', 'orange']
        for i, table in enumerate(sheet_data['tables']):
            color = colors[i % len(colors)]
            # 这里需要根据实际的表格区域数据来绘制边界框
            # 由于当前结构中没有保存表格的具体边界，这里做简化处理
            ax.text(0.1, 0.9 - i * 0.1, f'表格 {table["id"]}: {table["rows"]}x{table["cols"]}',
                    transform=ax.transAxes, fontsize=10, color=color,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.3))

        # 标注图例区域
        for i, legend in enumerate(sheet_data['legends']):
            color = colors[(i + len(sheet_data['tables'])) % len(colors)]
            ax.text(0.6, 0.9 - i * 0.1, f'图例 {legend["id"]}: {len(legend["items"])}项',
                    transform=ax.transAxes, fontsize=10, color=color,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.3))

        ax.set_title(title)
        ax.grid(True, alpha=0.3)

    def _identify_tables(self, region: Dict) -> List[Dict]:
        """识别表格区域"""
        tables = []

        # 查找包含"工程号"或"表格"关键字的文本
        table_keywords = ['工程号', '表格', '明细表', '材料表', '设备表']

        for text in region['texts']:
            for keyword in table_keywords:
                if keyword in text.content:
                    logger.info(f"发现表格关键字 '{keyword}' 在文本: {text.content}")
                    # 基于该文本位置查找表格区域
                    table_region = self._find_table_region(text.position, region)
                    if table_region:
                        tables.append(table_region)
                    break

        return tables

    def _find_table_region(self, anchor_pos: Point, region: Dict) -> Optional[Dict]:
        """基于锚点位置查找表格区域"""
        # 查找附近的网格线
        nearby_lines = []
        search_radius = 200  # 搜索半径

        for line in region['lines']:
            center_x = (line.start.x + line.end.x) / 2
            center_y = (line.start.y + line.end.y) / 2

            if (abs(center_x - anchor_pos.x) < search_radius and
                    abs(center_y - anchor_pos.y) < search_radius):
                if line.is_horizontal or line.is_vertical:
                    nearby_lines.append(line)

        logger.info(f"在锚点 ({anchor_pos.x:.2f}, {anchor_pos.y:.2f}) 附近找到 {len(nearby_lines)} 条网格线")

        if len(nearby_lines) < 4:  # 表格至少需要一些线
            return None

        # 计算表格边界
        x_coords = []
        y_coords = []
        for line in nearby_lines:
            x_coords.extend([line.start.x, line.end.x])
            y_coords.extend([line.start.y, line.end.y])

        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)

        # 查找该区域内的文本
        table_texts = []
        for text in region['texts']:
            if (x_min <= text.position.x <= x_max and
                    y_min <= text.position.y <= y_max):
                table_texts.append(text)

        logger.info(f"表格区域: X[{x_min:.2f}, {x_max:.2f}], Y[{y_min:.2f}, {y_max:.2f}], {len(table_texts)} 个文本")

        return {
            'x_min': x_min,
            'x_max': x_max,
            'y_min': y_min,
            'y_max': y_max,
            'lines': nearby_lines,
            'texts': table_texts
        }

    def _parse_table(self, table_region: Dict) -> Optional[Dict]:
        """解析表格结构"""
        if not table_region:
            return None

        # 提取水平和垂直线
        h_lines = [l for l in table_region['lines'] if l.is_horizontal]
        v_lines = [l for l in table_region['lines'] if l.is_vertical]

        logger.info(f"表格线段: {len(h_lines)} 条水平线, {len(v_lines)} 条垂直线")

        if not h_lines or not v_lines:
            return None

        # 获取行列边界
        y_coords = sorted(list(set([l.start.y for l in h_lines] + [l.end.y for l in h_lines])))
        x_coords = sorted(list(set([l.start.x for l in v_lines] + [l.end.x for l in v_lines])))

        # 去除过于接近的坐标
        def remove_close_coords(coords, threshold=3.0):
            if not coords:
                return coords

            filtered = [coords[0]]
            for coord in coords[1:]:
                if coord - filtered[-1] > threshold:
                    filtered.append(coord)
            return filtered

        y_coords = remove_close_coords(y_coords)
        x_coords = remove_close_coords(x_coords)

        # 创建表格矩阵
        rows = len(y_coords) - 1
        cols = len(x_coords) - 1

        logger.info(f"表格尺寸: {rows} 行 x {cols} 列")

        if rows <= 0 or cols <= 0:
            return None

        # 初始化表格
        table_data = [['' for _ in range(cols)] for _ in range(rows)]

        # 将文本填入单元格
        filled_cells = 0
        for text in table_region['texts']:
            # 找到文本所属的单元格
            row_idx = None
            col_idx = None

            # 从上到下查找行
            for i in range(rows):
                if y_coords[i + 1] <= text.position.y <= y_coords[i]:  # Y坐标从上到下递减
                    row_idx = i
                    break

            # 从左到右查找列
            for j in range(cols):
                if x_coords[j] <= text.position.x <= x_coords[j + 1]:
                    col_idx = j
                    break

            if row_idx is not None and col_idx is not None:
                if table_data[row_idx][col_idx]:
                    table_data[row_idx][col_idx] += ' ' + text.content
                else:
                    table_data[row_idx][col_idx] = text.content
                filled_cells += 1

        logger.info(f"成功填充 {filled_cells} 个单元格")

        return {
            'type': 'table',
            'data': table_data,
            'rows': rows,
            'cols': cols,
            'filled_cells': filled_cells
        }

    def _identify_legends(self, region: Dict) -> List[Dict]:
        """识别图例区域"""
        legends = []

        # 查找包含"图例"关键字的文本
        legend_keywords = ['图例', '说明', '符号说明']

        for text in region['texts']:
            for keyword in legend_keywords:
                if keyword in text.content:
                    logger.info(f"发现图例关键字 '{keyword}' 在文本: {text.content}")
                    # 基于该文本位置查找图例区域
                    legend_region = self._find_legend_region(text.position, region)
                    if legend_region:
                        legends.append(legend_region)
                    break

        return legends

    def _find_legend_region(self, anchor_pos: Point, region: Dict) -> Optional[Dict]:
        """基于锚点位置查找图例区域"""
        # 查找附近的文本
        nearby_texts = []
        search_radius = 100

        for text in region['texts']:
            if (abs(text.position.x - anchor_pos.x) < search_radius and
                    abs(text.position.y - anchor_pos.y) < search_radius):
                nearby_texts.append(text)

        logger.info(f"在锚点 ({anchor_pos.x:.2f}, {anchor_pos.y:.2f}) 附近找到 {len(nearby_texts)} 个文本")

        if nearby_texts:
            return {
                'texts': nearby_texts
            }

        return None

    def _parse_legend(self, legend_region: Dict) -> Optional[Dict]:
        """解析图例"""
        if not legend_region or not legend_region.get('texts'):
            return None

        legend_items = []
        for text in legend_region['texts']:
            if text.content.strip():
                legend_items.append(text.content.strip())

        return {
            'type': 'legend',
            'items': legend_items
        }

    def _format_output(self) -> Dict:
        """格式化输出结果"""
        output = {}

        for sheet_id, sheet_data in self.sheets.items():
            sheet_output = {
                '主图': {
                    '文本数量': sheet_data['main_drawing']['text_count'],
                    '线段数量': sheet_data['main_drawing']['line_count']
                },
                '表格': [],
                '图例': []
            }

            # 格式化表格
            for table in sheet_data['tables']:
                if table['type'] == 'table':
                    # 转换为DataFrame便于查看
                    df = pd.DataFrame(table['data'])

                    # 清理空行和空列
                    df = df.dropna(how='all').dropna(axis=1, how='all')

                    table_info = {
                        '表格ID': table['id'],
                        '尺寸': f"{table['rows']}行 x {table['cols']}列",
                        '填充单元格': table['filled_cells'],
                        '数据': df.to_dict('records') if not df.empty else []
                    }

                    sheet_output['表格'].append(table_info)

                    # 保存表格为CSV
                    if not df.empty:
                        csv_path = os.path.join(self.output_dir, f'sheet_{sheet_id}_table_{table["id"]}.csv')
                        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                        logger.info(f"表格已保存至: {csv_path}")

            # 格式化图例
            for legend in sheet_data['legends']:
                if legend['type'] == 'legend':
                    legend_info = {
                        '图例ID': legend['id'],
                        '项目数量': len(legend['items']),
                        '内容': legend['items']
                    }
                    sheet_output['图例'].append(legend_info)

            output[f'图纸{sheet_id}'] = sheet_output

        return output


def main():
    """主函数"""
    # 使用示例
    dxf_file = "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10.dxf"

    parser = DXFParser(dxf_file)
    result = parser.parse()

    # 打印结果
    import json
    print(json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main()