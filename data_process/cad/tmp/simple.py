import ezdxf
import json
import os
from collections import defaultdict
from typing import List, Dict, Any, Tuple
import numpy as np
from sklearn.cluster import KMeans

# 定义常量，提高代码可读性和可维护性
LAYER_TABLE_LINES = 'tk_btxt'  # 表格的线框图层
LAYER_TABLE_TEXTS = 'tk_btwz'  # 表格的文字图层
LAYER_CHART_TEXTS = 'syst'  # 图表数据的图层


class AdvancedDXFParser:
    """
    一个专注于多图纸划分、表格和图表解析的DXF解析器。
    根据指定的图层（tk_btxt, tk_btwz, syst）进行结构化信息提取。
    """

    def __init__(self, dxf_path: str):
        """
        初始化解析器。

        Args:
            dxf_path (str): DXF文件的路径。
        """
        if not os.path.exists(dxf_path):
            raise FileNotFoundError(f"错误: DXF文件未找到 -> {dxf_path}")
        self.dxf_path = dxf_path
        self.doc = None
        self.msp = None
        # 存储根据图层分类后的实体
        self.entities = {
            'table_lines': [],
            'table_texts': [],
            'chart_texts': [],
            'other': []
        }

    def _load_and_extract_entities(self):
        """加载DXF文件并根据指定图层提取和分类实体。"""
        print("步骤 1: 加载DXF文件并按图层分类实体...")
        try:
            self.doc = ezdxf.readfile(self.dxf_path)
            self.msp = self.doc.modelspace()
        except Exception as e:
            raise IOError(f"无法读取或解析DXF文件: {e}")

        # 遍历模型空间中的所有实体
        for entity in self.msp:
            layer = entity.dxf.get('layer', '').lower()
            if layer == LAYER_TABLE_LINES and entity.dxftype() in ['LINE', 'LWPOLYLINE']:
                self.entities['table_lines'].append(entity)
            elif layer == LAYER_TABLE_TEXTS and entity.dxftype() in ['TEXT', 'MTEXT']:
                self.entities['table_texts'].append(entity)
            elif layer == LAYER_CHART_TEXTS and entity.dxftype() in ['TEXT', 'MTEXT']:
                self.entities['chart_texts'].append(entity)
            else:
                self.entities['other'].append(entity)

        print(f"  - 表格线框 ({LAYER_TABLE_LINES}): {len(self.entities['table_lines'])} 个")
        print(f"  - 表格文字 ({LAYER_TABLE_TEXTS}): {len(self.entities['table_texts'])} 个")
        print(f"  - 图表文字 ({LAYER_CHART_TEXTS}): {len(self.entities['chart_texts'])} 个")
        print(f"  - 其他实体: {len(self.entities['other'])} 个")

    def _detect_sheets(self) -> List[Dict[str, Any]]:
        """
        使用聚类算法检测文件中的多个图纸区域。

        Returns:
            List[Dict[str, Any]]: 一个包含每个图纸边界信息的列表。
        """
        print("步骤 2: 检测文件中的独立图纸区域...")
        all_text_entities = self.entities['table_texts'] + self.entities['chart_texts']

        # 提取所有文本实体的坐标用于聚类
        coords = []
        for entity in all_text_entities:
            if hasattr(entity.dxf, 'insert'):
                coords.append(entity.dxf.insert[:2])  # 只取 x, y

        if not coords:
            print("  - 警告: 未找到足够的可用于划分图纸的文本实体，将视为单个图纸。")
            # 尝试使用所有实体的包围盒作为默认图纸
            try:
                bbox = ezdxf.bbox.extents(self.msp)
                return [{
                    "name": "图纸 1",
                    "bounds": {
                        "x_min": bbox.extmin.x, "y_min": bbox.extmin.y,
                        "x_max": bbox.extmax.x, "y_max": bbox.extmax.y
                    }
                }]
            except:
                return []

        coords_array = np.array(coords)
        x_range = np.ptp(coords_array[:, 0])

        # 根据X坐标范围判断可能的图纸数量
        # 这是一个经验值，如果图纸水平范围非常大，则可能是多张图纸并排
        num_clusters = 2 if x_range > 300000 else 1  # 阈值可根据实际情况调整

        if len(coords) < num_clusters * 2:  # 样本太少，不进行聚类
            num_clusters = 1

        print(f"  - 基于坐标分布，尝试检测 {num_clusters} 个图纸区域...")

        # 使用K-means聚类
        kmeans = KMeans(n_clusters=num_clusters, random_state=42, n_init='auto')
        labels = kmeans.fit_predict(coords_array)

        sheets = []
        for i in range(num_clusters):
            cluster_coords = coords_array[labels == i]
            if len(cluster_coords) > 0:
                x_min, y_min = np.min(cluster_coords, axis=0)
                x_max, y_max = np.max(cluster_coords, axis=0)

                # 基于文本的边界，我们可以扩大一些范围来捕获所有相关实体
                padding_x = (x_max - x_min) * 0.10  # 10%的水平外扩
                padding_y = (y_max - y_min) * 0.10  # 10%的垂直外扩

                sheets.append({
                    "name": f"图纸 {i + 1}",
                    "bounds": {
                        "x_min": x_min - padding_x, "y_min": y_min - padding_y,
                        "x_max": x_max + padding_x, "y_max": y_max + padding_y
                    }
                })

        print(f"  - 成功检测到 {len(sheets)} 个图纸区域。")
        return sheets

    def _parse_tables_in_sheet(self, sheet_bounds: Dict) -> List[Dict[str, Any]]:
        """
        在给定的图纸区域内解析表格。

        Args:
            sheet_bounds (Dict): 图纸的边界框。

        Returns:
            List[Dict[str, Any]]: 解析出的表格数据列表。
        """
        # 1. 筛选出位于当前图纸区域内的表格线和文字
        lines_in_sheet = [
            line for line in self.entities['table_lines']
            if self._is_entity_in_bounds(line, sheet_bounds)
        ]
        texts_in_sheet = [
            text for text in self.entities['table_texts']
            if self._is_entity_in_bounds(text, sheet_bounds)
        ]

        if not lines_in_sheet or not texts_in_sheet:
            return []

        # 2. 投影法：获取所有水平和垂直线的坐标
        x_coords, y_coords = set(), set()
        tolerance = 1.0  # 坐标容差
        for line in lines_in_sheet:
            start, end = line.dxf.start, line.dxf.end
            x_coords.add(start.x)
            x_coords.add(end.x)
            y_coords.add(start.y)
            y_coords.add(end.y)

        # 坐标去重和排序
        sorted_x = sorted(list(x_coords))
        sorted_y = sorted(list(y_coords), reverse=True)  # Y轴从上到下排序

        # 过滤掉过于接近的坐标线
        unique_x = [sorted_x[0]]
        for x in sorted_x[1:]:
            if x - unique_x[-1] > tolerance:
                unique_x.append(x)

        unique_y = [sorted_y[0]]
        for y in sorted_y[1:]:
            if unique_y[-1] - y > tolerance:
                unique_y.append(y)

        if len(unique_x) < 2 or len(unique_y) < 2:
            return []  # 无法构成网格

        # 3. 创建单元格网格
        num_cols = len(unique_x) - 1
        num_rows = len(unique_y) - 1
        # 初始化一个二维列表来存储单元格内容
        table_grid = [["" for _ in range(num_cols)] for _ in range(num_rows)]

        # 4. 将文本放入对应的单元格
        for text in texts_in_sheet:
            tx, ty = text.dxf.insert.x, text.dxf.insert.y

            # 找到文本所在的行和列
            row_idx, col_idx = -1, -1
            for i in range(num_rows):
                if unique_y[i] > ty > unique_y[i + 1]:
                    row_idx = i
                    break
            for j in range(num_cols):
                if unique_x[j] < tx < unique_x[j + 1]:
                    col_idx = j
                    break

            # 如果找到了单元格，则填入文本
            if row_idx != -1 and col_idx != -1:
                # 清理MTEXT格式代码
                clean_text = text.plain_text() if text.dxftype() == 'MTEXT' else text.dxf.text
                table_grid[row_idx][col_idx] = clean_text.strip()

        return [{
            "name": "解析的表格",
            "rows": len(table_grid),
            "columns": len(table_grid[0]) if table_grid else 0,
            "data": table_grid
        }]

    def _parse_charts_in_sheet(self, sheet_bounds: Dict) -> List[Dict[str, Any]]:
        """在给定的图纸区域内解析图表（从syst图层提取文本）。"""
        texts_in_sheet = [
            text for text in self.entities['chart_texts']
            if self._is_entity_in_bounds(text, sheet_bounds)
        ]

        if not texts_in_sheet:
            return []

        chart_data = []
        for text in texts_in_sheet:
            clean_text = text.plain_text() if text.dxftype() == 'MTEXT' else text.dxf.text
            chart_data.append({
                "text": clean_text.strip(),
                "position": [text.dxf.insert.x, text.dxf.insert.y]
            })

        return [{
            "name": "解析的图表",
            "text_count": len(chart_data),
            "data": chart_data
        }]

    def _get_main_drawing_entities_info(self, sheet_bounds: Dict) -> Dict[str, Any]:
        """获取主图实体信息统计。"""
        entities_in_sheet = [
            entity for entity in self.entities['other']
            if self._is_entity_in_bounds(entity, sheet_bounds)
        ]

        layer_stats = defaultdict(int)
        type_stats = defaultdict(int)
        for entity in entities_in_sheet:
            layer_stats[entity.dxf.get('layer', '0')] += 1
            type_stats[entity.dxftype()] += 1

        return {
            "entity_count": len(entities_in_sheet),
            "layer_statistics": dict(layer_stats),
            "type_statistics": dict(type_stats)
        }

    def _is_entity_in_bounds(self, entity, bounds: Dict) -> bool:
        """检查实体是否在给定的边界内。"""
        x_min, y_min, x_max, y_max = bounds['x_min'], bounds['y_min'], bounds['x_max'], bounds['y_max']

        # 尝试获取实体的一个代表性坐标点
        pos = None
        if hasattr(entity.dxf, 'insert'):
            pos = entity.dxf.insert
        elif hasattr(entity.dxf, 'center'):
            pos = entity.dxf.center
        elif hasattr(entity.dxf, 'start'):
            pos = entity.dxf.start

        if pos:
            return x_min <= pos.x <= x_max and y_min <= pos.y <= y_max

        # 对于没有单一位置的复杂实体（如多段线），检查其包围盒
        try:
            bbox = ezdxf.bbox.extents([entity])
            return (x_min <= bbox.extmin.x and bbox.extmax.x <= x_max and
                    y_min <= bbox.extmin.y and bbox.extmax.y <= y_max)
        except:
            return False  # 无法计算包围盒，则忽略

    def parse(self) -> Dict[str, Any]:
        """
        执行完整的解析流程。

        Returns:
            Dict[str, Any]: 包含所有解析信息的结构化字典。
        """
        # 1. 加载和分类
        self._load_and_extract_entities()

        # 2. 划分图纸
        sheets = self._detect_sheets()

        # 3. 为每个图纸解析内容
        print("步骤 3: 为每个图纸区域解析内容...")
        parsed_sheets_data = []
        for sheet in sheets:
            print(f"  - 正在处理 {sheet['name']}...")
            sheet_bounds = sheet['bounds']

            # 解析表格
            tables = self._parse_tables_in_sheet(sheet_bounds)

            # 解析图表
            charts = self._parse_charts_in_sheet(sheet_bounds)

            # 获取主图信息
            main_drawing_info = self._get_main_drawing_entities_info(sheet_bounds)

            parsed_sheets_data.append({
                "sheet_name": sheet['name'],
                "sheet_bounds": sheet_bounds,
                "main_drawing": main_drawing_info,
                "charts": charts,
                "tables": tables,
            })

        print("解析流程完成。")

        # 4. 组装最终输出
        final_output = {
            "file_name": os.path.basename(self.dxf_path),
            "parsing_summary": {
                "total_sheets_detected": len(sheets),
            },
            "sheets_data": parsed_sheets_data
        }
        return final_output


def main():
    """主执行函数"""
    # --- 配置区域 ---
    # 请将此路径替换为您的DXF文件路径
    input_dxf_file = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10.dxf'
    # 输出的JSON文件名将基于输入文件名
    output_json_file = f"{os.path.splitext(input_dxf_file)[0]}_parsed_output.json"
    # --- 配置结束 ---


    try:
        # 1. 初始化解析器
        parser = AdvancedDXFParser(input_dxf_file)

        # 2. 执行解析
        parsed_data = parser.parse()

        # 3. 保存到JSON文件
        print(f"\n步骤 4: 将结果保存到JSON文件 -> {output_json_file}")
        with open(output_json_file, 'w', encoding='utf-8') as f:
            json.dump(parsed_data, f, ensure_ascii=False, indent=4)

        print("\n=== 所有操作成功完成！ ===")
        print(f"查看 {output_json_file} 获取详细的结构化数据。")
        return 0

    except Exception as e:
        print(f"\n--- 程序执行过程中发生严重错误 ---")
        print(str(e))
        return 1


if __name__ == '__main__':
    # 确保已安装必要的库
    try:
        import numpy
        import sklearn
    except ImportError:
        print("错误: 缺少必要的库。请运行 'pip install numpy scikit-learn' 来安装。")
        exit(1)

    exit(main())