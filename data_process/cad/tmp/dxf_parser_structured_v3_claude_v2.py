import ezdxf
import json
import numpy as np
import matplotlib.pyplot as plt
import logging
from collections import defaultdict
from typing import Dict, List, Any, Optional, Tuple, Union
from pathlib import Path
from dataclasses import dataclass
import os
from PIL import Image, ImageDraw

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dxf_parser.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class Rectangle:
    """矩形框类"""
    x_min: float
    y_min: float
    x_max: float
    y_max: float
    width: float
    height: float
    area: float
    lines: List[Dict]  # 组成矩形的线段

    def __post_init__(self):
        self.center_x = (self.x_min + self.x_max) / 2
        self.center_y = (self.y_min + self.y_max) / 2

    def contains_point(self, x: float, y: float, tolerance: float = 0) -> bool:
        """判断点是否在矩形内"""
        return (self.x_min - tolerance <= x <= self.x_max + tolerance and
                self.y_min - tolerance <= y <= self.y_max + tolerance)

    def contains_rectangle(self, other: 'Rectangle', tolerance: float = 10) -> bool:
        """判断是否包含另一个矩形"""
        return (self.x_min <= other.x_min + tolerance and
                self.y_min <= other.y_min + tolerance and
                self.x_max >= other.x_max - tolerance and
                self.y_max >= other.y_max - tolerance)

    def overlaps_with(self, other: 'Rectangle', tolerance: float = 0) -> bool:
        """判断是否与另一个矩形重叠"""
        return not (self.x_max + tolerance < other.x_min or
                    other.x_max + tolerance < self.x_min or
                    self.y_max + tolerance < other.y_min or
                    other.y_max + tolerance < self.y_min)


class DXFRectangleBasedParser:
    """
    基于外框线的DXF解析器
    """

    def __init__(self, dxf_path: str):
        self.dxf_path = dxf_path
        self.doc = None

        # 实体存储
        self.all_entities = []
        self.line_entities = []
        self.text_entities = []

        # 矩形检测结果
        self.all_rectangles = []
        self.main_frames = []  # 主要外框（图纸边界）
        self.table_frames = []  # 表格框

        # 图纸区域
        self.drawing_sheets = []

        # 参数设置
        self.line_tolerance = 5.0  # 线段连接容差
        self.rectangle_min_size = 1000.0  # 最小矩形尺寸
        self.table_min_size = 5000.0  # 表格最小尺寸
        self.main_frame_min_size = 50000.0  # 主框架最小尺寸

        # 关键词定义
        self.legend_keywords = [
            "图例",
            # "符号", "说明", "标识", "legend", "图标", "标记",
            # "符号说明", "图形符号", "标准符号", "图示", "示意"
        ]

        self.table_keywords = [
            "工程号",
            # "项目号", "图号", "设计", "审核", "校对", "批准",
            # "工程名称", "项目名称", "图纸名称", "比例", "日期", "版本"
        ]

    def load_document(self) -> bool:
        """加载DXF文档"""
        try:
            self.doc = ezdxf.readfile(self.dxf_path)
            logger.info(f"✓ 成功加载DXF文件: {self.dxf_path}")
            return True
        except Exception as e:
            logger.error(f"✗ 加载DXF文件失败: {e}")
            return False

    def extract_all_entities(self):
        """提取所有实体"""
        self.all_entities = []
        self.line_entities = []
        self.text_entities = []

        # 处理所有空间
        spaces = [("模型空间", self.doc.modelspace())]
        for layout in self.doc.layouts:
            if layout.name != 'Model':
                spaces.append((f"图纸空间-{layout.name}", layout))

        for space_name, space in spaces:
            for entity in space:
                self._process_entity(entity, space_name)

        logger.info(
            f"实体提取完成: 总数={len(self.all_entities)}, 线段={len(self.line_entities)}, 文本={len(self.text_entities)}")

    def _process_entity(self, entity, space_name: str):
        """处理单个实体"""
        entity_type = entity.dxftype()

        try:
            entity_info = {
                "实体类型": entity_type,
                "所在图层": getattr(entity.dxf, 'layer', ''),
                "颜色索引": getattr(entity.dxf, 'color', 0),
                "空间名称": space_name
            }

            # 处理线段实体
            if entity_type == 'LINE':
                line_info = self._extract_line_entity(entity)
                if line_info:
                    entity_info.update(line_info)
                    self.line_entities.append(entity_info)

            # 处理文本实体
            elif entity_type in ['TEXT', 'MTEXT', 'ATTRIB', 'ATTDEF']:
                text_info = self._extract_text_entity(entity)
                if text_info:
                    entity_info.update(text_info)
                    self.text_entities.append(entity_info)

            # 处理块实体
            elif entity_type == 'INSERT':
                block_info = self._extract_block_entity(entity)
                if block_info:
                    entity_info.update(block_info)
                    if block_info.get("属性文本"):
                        self.text_entities.append(entity_info)

            # 处理多段线（可能形成矩形）
            elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                poly_info = self._extract_polyline_entity(entity)
                if poly_info:
                    entity_info.update(poly_info)
                    # 如果多段线是矩形，转换为线段
                    if poly_info.get("是否矩形"):
                        rect_lines = self._polyline_to_lines(entity, entity_info)
                        self.line_entities.extend(rect_lines)

            self.all_entities.append(entity_info)

        except Exception as e:
            logger.warning(f"处理实体时出错 {entity_type}: {e}")

    def _extract_line_entity(self, entity) -> Optional[Dict]:
        """提取线段实体信息"""
        try:
            start_point = self._safe_get_coords(entity.dxf.start)
            end_point = self._safe_get_coords(entity.dxf.end)

            if not start_point or not end_point or len(start_point) < 2 or len(end_point) < 2:
                return None

            # 计算线段属性
            dx = end_point[0] - start_point[0]
            dy = end_point[1] - start_point[1]
            length = (dx ** 2 + dy ** 2) ** 0.5

            if length < 1.0:  # 过短的线段忽略
                return None

            line_info = {
                "起点": start_point,
                "终点": end_point,
                "长度": length,
                "方向向量": [dx, dy]
            }

            # 判断线段方向
            if abs(dy) < self.line_tolerance:
                line_info["方向"] = "水平"
                line_info["Y坐标"] = (start_point[1] + end_point[1]) / 2
                line_info["X范围"] = [min(start_point[0], end_point[0]), max(start_point[0], end_point[0])]
            elif abs(dx) < self.line_tolerance:
                line_info["方向"] = "垂直"
                line_info["X坐标"] = (start_point[0] + end_point[0]) / 2
                line_info["Y范围"] = [min(start_point[1], end_point[1]), max(start_point[1], end_point[1])]
            else:
                line_info["方向"] = "斜线"

            return line_info

        except Exception as e:
            logger.warning(f"提取线段信息失败: {e}")
            return None

    def _extract_text_entity(self, entity) -> Optional[Dict]:
        """提取文本实体信息"""
        entity_type = entity.dxftype()

        try:
            text_info = {}

            if entity_type == 'TEXT':
                text_info["文本内容"] = self._clean_text(entity.dxf.text)
                text_info["插入点"] = self._safe_get_coords(entity.dxf.insert)
                text_info["文本高度"] = getattr(entity.dxf, 'height', 0)

            elif entity_type == 'MTEXT':
                text_info["文本内容"] = self._clean_text(entity.plain_text())
                text_info["插入点"] = self._safe_get_coords(entity.dxf.insert)
                text_info["字符高度"] = getattr(entity.dxf, 'char_height', 0)

            elif entity_type in ['ATTRIB', 'ATTDEF']:
                text_info["文本内容"] = self._clean_text(entity.dxf.text)
                text_info["插入点"] = self._safe_get_coords(entity.dxf.insert)
                text_info["标签"] = getattr(entity.dxf, 'tag', '')
                text_info["文本高度"] = getattr(entity.dxf, 'height', 0)

            return text_info if text_info.get("文本内容") else None

        except Exception as e:
            logger.warning(f"提取文本信息失败: {e}")
            return None

    def _extract_block_entity(self, entity) -> Optional[Dict]:
        """提取块实体信息"""
        try:
            block_info = {
                "块名称": getattr(entity.dxf, 'name', ''),
                "插入点": self._safe_get_coords(entity.dxf.insert),
                "缩放比例": [
                    getattr(entity.dxf, 'xscale', 1.0),
                    getattr(entity.dxf, 'yscale', 1.0),
                    getattr(entity.dxf, 'zscale', 1.0)
                ]
            }

            # 提取块属性
            attribute_texts = []
            if hasattr(entity, 'attribs'):
                for attrib in entity.attribs:
                    if hasattr(attrib.dxf, 'text') and attrib.dxf.text:
                        attr_text = self._clean_text(attrib.dxf.text)
                        if attr_text:
                            attribute_texts.append(attr_text)

            if attribute_texts:
                block_info["属性文本"] = "; ".join(attribute_texts)
                block_info["文本内容"] = block_info["属性文本"]

            return block_info

        except Exception as e:
            logger.warning(f"提取块信息失败: {e}")
            return None

    def _extract_polyline_entity(self, entity) -> Optional[Dict]:
        """提取多段线实体信息"""
        try:
            poly_info = {
                "是否闭合": entity.is_closed,
                "顶点数量": len(entity)
            }

            # 获取顶点坐标
            vertices = []
            for vertex in entity:
                if hasattr(vertex, 'dxf'):
                    coords = self._safe_get_coords(vertex.dxf.location)
                else:
                    coords = self._safe_get_coords(vertex)

                if coords and len(coords) >= 2:
                    vertices.append(coords)

            poly_info["顶点列表"] = vertices

            # 检查是否为矩形
            if len(vertices) >= 4 and entity.is_closed:
                poly_info["是否矩形"] = self._is_rectangle_polyline(vertices)

            return poly_info

        except Exception as e:
            logger.warning(f"提取多段线信息失败: {e}")
            return None

    def _polyline_to_lines(self, entity, entity_info: Dict) -> List[Dict]:
        """将矩形多段线转换为线段"""
        lines = []
        vertices = entity_info.get("顶点列表", [])

        if len(vertices) < 3:
            return lines

        # 创建线段
        for i in range(len(vertices)):
            start_vertex = vertices[i]
            end_vertex = vertices[(i + 1) % len(vertices)]

            dx = end_vertex[0] - start_vertex[0]
            dy = end_vertex[1] - start_vertex[1]
            length = (dx ** 2 + dy ** 2) ** 0.5

            if length > 1.0:  # 忽略过短的线段
                line_info = {
                    "实体类型": "LINE",
                    "所在图层": entity_info.get("所在图层", ""),
                    "颜色索引": entity_info.get("颜色索引", 0),
                    "空间名称": entity_info.get("空间名称", ""),
                    "起点": start_vertex,
                    "终点": end_vertex,
                    "长度": length,
                    "方向向量": [dx, dy],
                    "来源": "多段线转换"
                }

                # 判断方向
                if abs(dy) < self.line_tolerance:
                    line_info["方向"] = "水平"
                    line_info["Y坐标"] = (start_vertex[1] + end_vertex[1]) / 2
                    line_info["X范围"] = [min(start_vertex[0], end_vertex[0]), max(start_vertex[0], end_vertex[0])]
                elif abs(dx) < self.line_tolerance:
                    line_info["方向"] = "垂直"
                    line_info["X坐标"] = (start_vertex[0] + end_vertex[0]) / 2
                    line_info["Y范围"] = [min(start_vertex[1], end_vertex[1]), max(start_vertex[1], end_vertex[1])]
                else:
                    line_info["方向"] = "斜线"

                lines.append(line_info)

        return lines

    def _is_rectangle_polyline(self, vertices: List[List[float]]) -> bool:
        """判断多段线是否为矩形"""
        if len(vertices) < 4:
            return False

        # 检查是否有4个直角
        angles = []
        for i in range(len(vertices)):
            p1 = vertices[i]
            p2 = vertices[(i + 1) % len(vertices)]
            p3 = vertices[(i + 2) % len(vertices)]

            # 计算角度
            v1 = [p1[0] - p2[0], p1[1] - p2[1]]
            v2 = [p3[0] - p2[0], p3[1] - p2[1]]

            dot_product = v1[0] * v2[0] + v1[1] * v2[1]
            mag1 = (v1[0] ** 2 + v1[1] ** 2) ** 0.5
            mag2 = (v2[0] ** 2 + v2[1] ** 2) ** 0.5

            if mag1 > 0 and mag2 > 0:
                cos_angle = dot_product / (mag1 * mag2)
                cos_angle = max(-1, min(1, cos_angle))  # 限制范围
                angle = np.arccos(cos_angle) * 180 / np.pi
                angles.append(angle)

        # 检查是否都接近90度
        right_angle_count = sum(1 for angle in angles if abs(angle - 90) < 10)
        return right_angle_count >= 3  # 至少3个直角

    def _safe_get_coords(self, coord_obj) -> Optional[List[float]]:
        """安全获取坐标"""
        if coord_obj is None:
            return None

        try:
            if isinstance(coord_obj, (list, tuple)):
                return [float(x) for x in coord_obj]
            elif hasattr(coord_obj, 'x') and hasattr(coord_obj, 'y'):
                z = getattr(coord_obj, 'z', 0.0)
                return [float(coord_obj.x), float(coord_obj.y), float(z)]
            elif hasattr(coord_obj, '__iter__'):
                return [float(x) for x in coord_obj]
            else:
                return [float(coord_obj)]
        except:
            return None

    def _clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        import re
        text = re.sub(r'\\[A-Za-z][0-9]*;?', '', text)
        text = re.sub(r'\\[{}]', '', text)
        text = re.sub(r'\\P', '\n', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()

    def detect_rectangles(self):
        """检测所有矩形外框"""
        logger.info("开始检测矩形外框...")

        # 分离水平线和垂直线
        horizontal_lines = [line for line in self.line_entities if line.get("方向") == "水平"]
        vertical_lines = [line for line in self.line_entities if line.get("方向") == "垂直"]

        logger.info(f"水平线数量: {len(horizontal_lines)}, 垂直线数量: {len(vertical_lines)}")

        if len(horizontal_lines) < 2 or len(vertical_lines) < 2:
            logger.warning("水平线或垂直线数量不足，无法构成矩形")
            return

        # 检测矩形
        rectangles = self._find_rectangles_from_lines(horizontal_lines, vertical_lines)

        # 过滤和分类矩形
        self._classify_rectangles(rectangles)

        logger.info(f"检测到矩形总数: {len(self.all_rectangles)}")
        logger.info(f"主框架数量: {len(self.main_frames)}")
        logger.info(f"表格框数量: {len(self.table_frames)}")

    def _find_rectangles_from_lines(self, h_lines: List[Dict], v_lines: List[Dict]) -> List[Rectangle]:
        """从线段中查找矩形"""
        rectangles = []

        # 按坐标分组线段
        h_groups = defaultdict(list)  # 按Y坐标分组水平线
        v_groups = defaultdict(list)  # 按X坐标分组垂直线

        for line in h_lines:
            y_coord = round(line["Y坐标"] / self.line_tolerance) * self.line_tolerance
            h_groups[y_coord].append(line)

        for line in v_lines:
            x_coord = round(line["X坐标"] / self.line_tolerance) * self.line_tolerance
            v_groups[x_coord].append(line)

        # 尝试组合成矩形
        h_coords = sorted(h_groups.keys())
        v_coords = sorted(v_groups.keys())

        for i, y1 in enumerate(h_coords):
            for j, y2 in enumerate(h_coords[i + 1:], i + 1):
                for k, x1 in enumerate(v_coords):
                    for l, x2 in enumerate(v_coords[k + 1:], k + 1):
                        # 检查是否能形成矩形
                        rect = self._check_rectangle_formation(
                            x1, y1, x2, y2, h_groups, v_groups
                        )
                        if rect:
                            rectangles.append(rect)

        # 去重和过滤
        rectangles = self._remove_duplicate_rectangles(rectangles)
        rectangles = [rect for rect in rectangles if rect.area >= self.rectangle_min_size]

        return rectangles

    def _check_rectangle_formation(self, x1: float, y1: float, x2: float, y2: float,
                                   h_groups: Dict, v_groups: Dict) -> Optional[Rectangle]:
        """检查四条线是否能形成矩形"""
        tolerance = self.line_tolerance

        # 确保x1 < x2, y1 < y2
        if x1 > x2:
            x1, x2 = x2, x1
        if y1 > y2:
            y1, y2 = y2, y1

        width = x2 - x1
        height = y2 - y1

        if width < self.rectangle_min_size or height < self.rectangle_min_size:
            return None

        # 查找四条边
        edges_found = []

        # 上边 (y2)
        for line in h_groups.get(y2, []):
            x_range = line["X范围"]
            if (abs(x_range[0] - x1) <= tolerance and abs(x_range[1] - x2) <= tolerance):
                edges_found.append(line)
                break

        # 下边 (y1)
        for line in h_groups.get(y1, []):
            x_range = line["X范围"]
            if (abs(x_range[0] - x1) <= tolerance and abs(x_range[1] - x2) <= tolerance):
                edges_found.append(line)
                break

        # 左边 (x1)
        for line in v_groups.get(x1, []):
            y_range = line["Y范围"]
            if (abs(y_range[0] - y1) <= tolerance and abs(y_range[1] - y2) <= tolerance):
                edges_found.append(line)
                break

        # 右边 (x2)
        for line in v_groups.get(x2, []):
            y_range = line["Y范围"]
            if (abs(y_range[0] - y1) <= tolerance and abs(y_range[1] - y2) <= tolerance):
                edges_found.append(line)
                break

        # 需要找到至少3条边才认为是矩形（允许一条边缺失）
        if len(edges_found) >= 3:
            return Rectangle(
                x_min=x1, y_min=y1, x_max=x2, y_max=y2,
                width=width, height=height, area=width * height,
                lines=edges_found
            )

        return None

    def _remove_duplicate_rectangles(self, rectangles: List[Rectangle]) -> List[Rectangle]:
        """去除重复的矩形"""
        unique_rectangles = []
        tolerance = self.line_tolerance * 2

        for rect in rectangles:
            is_duplicate = False
            for existing in unique_rectangles:
                if (abs(rect.x_min - existing.x_min) <= tolerance and
                        abs(rect.y_min - existing.y_min) <= tolerance and
                        abs(rect.x_max - existing.x_max) <= tolerance and
                        abs(rect.y_max - existing.y_max) <= tolerance):
                    is_duplicate = True
                    break

            if not is_duplicate:
                unique_rectangles.append(rect)

        return unique_rectangles

    def _classify_rectangles(self, rectangles: List[Rectangle]):
        """分类矩形：主框架 vs 表格框"""
        self.all_rectangles = sorted(rectangles, key=lambda r: r.area, reverse=True)
        self.main_frames = []
        self.table_frames = []

        # 主框架：面积最大的几个矩形
        for rect in self.all_rectangles:
            if rect.area >= self.main_frame_min_size:
                self.main_frames.append(rect)
            elif rect.area >= self.table_min_size:
                self.table_frames.append(rect)

        # 如果没有找到主框架，降低标准
        if not self.main_frames and self.all_rectangles:
            # 取最大的1-2个矩形作为主框架
            max_area = self.all_rectangles[0].area
            for rect in self.all_rectangles[:3]:  # 最多取前3个
                if rect.area >= max_area * 0.5:  # 面积至少是最大面积的50%
                    self.main_frames.append(rect)
                    if rect in self.table_frames:
                        self.table_frames.remove(rect)

        logger.info(f"主框架详情:")
        for i, frame in enumerate(self.main_frames):
            logger.info(f"  主框架{i + 1}: 面积={frame.area:.0f}, 尺寸={frame.width:.0f}x{frame.height:.0f}")

        logger.info(f"表格框详情:")
        for i, frame in enumerate(self.table_frames[:5]):  # 只显示前5个
            logger.info(f"  表格框{i + 1}: 面积={frame.area:.0f}, 尺寸={frame.width:.0f}x{frame.height:.0f}")

    def create_drawing_sheets_from_frames(self):
        """基于主框架创建图纸区域"""
        logger.info("基于主框架创建图纸区域...")

        if not self.main_frames:
            logger.warning("未找到主框架，无法创建图纸区域")
            return

        self.drawing_sheets = []

        # 如果只有一个主框架，检查是否可以分割
        if len(self.main_frames) == 1:
            main_frame = self.main_frames[0]

            # 检查框架内的文本分布
            texts_in_frame = self._get_texts_in_rectangle(main_frame)

            if len(texts_in_frame) > 50:  # 文本数量足够多，尝试分割
                sub_sheets = self._split_frame_by_text_distribution(main_frame, texts_in_frame)
                if len(sub_sheets) > 1:
                    self.drawing_sheets.extend(sub_sheets)
                else:
                    # 无法分割，作为单个图纸
                    self.drawing_sheets.append(self._create_sheet_from_frame(main_frame, "图纸1", 1))
            else:
                self.drawing_sheets.append(self._create_sheet_from_frame(main_frame, "图纸1", 1))

        else:
            # 多个主框架，每个作为一个图纸
            for i, frame in enumerate(self.main_frames):
                sheet = self._create_sheet_from_frame(frame, f"图纸{i + 1}", i + 1)
                self.drawing_sheets.append(sheet)

        logger.info(f"创建了 {len(self.drawing_sheets)} 个图纸区域")

    def _get_texts_in_rectangle(self, rect: Rectangle, tolerance: float = 0) -> List[Dict]:
        """获取矩形内的文本实体"""
        texts = []
        for text in self.text_entities:
            coords = text.get("插入点")
            if coords and len(coords) >= 2:
                if rect.contains_point(coords[0], coords[1], tolerance):
                    texts.append(text)
        return texts

    def _split_frame_by_text_distribution(self, frame: Rectangle, texts: List[Dict]) -> List[Dict]:
        """根据文本分布分割框架"""
        if len(texts) < 20:
            return []

        # 提取文本X坐标
        x_coords = [text["插入点"][0] for text in texts if text.get("插入点")]

        if not x_coords:
            return []

        # 寻找X坐标的间隙
        sorted_x = sorted(x_coords)
        gaps = []

        for i in range(len(sorted_x) - 1):
            gap_size = sorted_x[i + 1] - sorted_x[i]
            if gap_size > (frame.width * 0.1):  # 间隙大于框架宽度的10%
                gaps.append((sorted_x[i], sorted_x[i + 1], gap_size))

        if gaps:
            # 选择最大的间隙作为分割点
            largest_gap = max(gaps, key=lambda x: x[2])
            split_x = (largest_gap[0] + largest_gap[1]) / 2

            # 分割文本
            left_texts = [t for t in texts if t["插入点"][0] < split_x]
            right_texts = [t for t in texts if t["插入点"][0] >= split_x]

            if len(left_texts) >= 10 and len(right_texts) >= 10:
                # 创建两个子图纸
                left_sheet = {
                    "图纸名称": "图纸1",
                    "图纸类型": "左侧图纸",
                    "主框架": Rectangle(
                        frame.x_min, frame.y_min, split_x, frame.y_max,
                        split_x - frame.x_min, frame.height,
                        (split_x - frame.x_min) * frame.height, []
                    ),
                    "文本数量": len(left_texts),
                    "分割方法": "文本分布分析"
                }

                right_sheet = {
                    "图纸名称": "图纸2",
                    "图纸类型": "右侧图纸",
                    "主框架": Rectangle(
                        split_x, frame.y_min, frame.x_max, frame.y_max,
                        frame.x_max - split_x, frame.height,
                        (frame.x_max - split_x) * frame.height, []
                    ),
                    "文本数量": len(right_texts),
                    "分割方法": "文本分布分析"
                }

                return [left_sheet, right_sheet]

        return []

    def _create_sheet_from_frame(self, frame: Rectangle, name: str, index: int) -> Dict:
        """从框架创建图纸"""
        return {
            "图纸名称": name,
            "图纸类型": "主图纸",
            "图纸索引": index,
            "主框架": frame,
            "X范围": [frame.x_min, frame.x_max],
            "Y范围": [frame.y_min, frame.y_max],
            "面积": frame.area,
            "尺寸": [frame.width, frame.height]
        }

    def detect_tables_and_legends_in_sheets(self):
        """在每个图纸中检测表格和图例"""
        logger.info("开始检测表格和图例...")

        for sheet in self.drawing_sheets:
            sheet_name = sheet["图纸名称"]
            main_frame = sheet["主框架"]

            logger.info(f"分析 {sheet_name}...")

            # 获取图纸内的表格框
            sheet_table_frames = []
            for table_frame in self.table_frames:
                if main_frame.contains_rectangle(table_frame, tolerance=100):
                    sheet_table_frames.append(table_frame)

            # 获取图纸内的文本
            sheet_texts = self._get_texts_in_rectangle(main_frame, tolerance=50)

            # 分析表格和图例
            tables, legends, main_drawing_texts = self._analyze_content_in_sheet(
                sheet_table_frames, sheet_texts, main_frame
            )

            # 添加到图纸信息中
            sheet["表格列表"] = tables
            sheet["图例列表"] = legends
            sheet["主图文本"] = main_drawing_texts
            sheet["表格数量"] = len(tables)
            sheet["图例数量"] = len(legends)
            sheet["主图文本数量"] = len(main_drawing_texts)

            logger.info(f"  {sheet_name}: 表格={len(tables)}, 图例={len(legends)}, 主图文本={len(main_drawing_texts)}")

            # 检查是否缺少关键内容
            self._validate_sheet_content(sheet)

    def _analyze_content_in_sheet(self, table_frames: List[Rectangle], texts: List[Dict],
                                  main_frame: Rectangle) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """分析图纸内容：表格、图例、主图文本"""
        tables = []
        legends = []
        main_drawing_texts = []
        assigned_texts = set()

        # 1. 处理表格框内的内容
        for i, table_frame in enumerate(table_frames):
            table_texts = self._get_texts_in_rectangle(table_frame, tolerance=20)

            if table_texts:
                # 分析表格内容类型
                table_content = " ".join([t.get("文本内容", "") for t in table_texts])
                table_type = self._classify_content_type(table_content)

                table_info = {
                    "表格ID": i + 1,
                    "表格名称": f"表格{i + 1}",
                    "表格类型": table_type,
                    "框架": table_frame,
                    "文本数量": len(table_texts),
                    "文本内容": table_texts,
                    "边界": [table_frame.x_min, table_frame.y_min, table_frame.x_max, table_frame.y_max]
                }

                if table_type == "图例":
                    legends.append(table_info)
                else:
                    tables.append(table_info)

                # 标记已分配的文本
                for text in table_texts:
                    assigned_texts.add(id(text))

        # 2. 处理框架外的文本（可能是图例或主图内容）
        unassigned_texts = [t for t in texts if id(t) not in assigned_texts]

        # 按位置和内容分组
        legend_texts = []
        for text in unassigned_texts:
            content = text.get("文本内容", "")
            if self._is_legend_content(content):
                legend_texts.append(text)
                assigned_texts.add(id(text))

        # 如果有独立的图例文本，创建图例区域
        if legend_texts:
            legend_info = {
                "表格ID": len(legends) + 1,
                "表格名称": f"图例{len(legends) + 1}",
                "表格类型": "图例",
                "框架": None,
                "文本数量": len(legend_texts),
                "文本内容": legend_texts,
                "边界": self._get_texts_bounding_box(legend_texts)
            }
            legends.append(legend_info)

        # 3. 剩余文本归为主图内容
        main_drawing_texts = [t for t in texts if id(t) not in assigned_texts]

        return tables, legends, main_drawing_texts

    def _classify_content_type(self, content: str) -> str:
        """分类内容类型"""
        content_lower = content.lower()

        # 检查图例关键词
        if any(keyword.lower() in content_lower for keyword in self.legend_keywords):
            return "图例"

        # 检查表格关键词
        if any(keyword.lower() in content_lower for keyword in self.table_keywords):
            return "工程表格"

        return "通用表格"

    def _is_legend_content(self, content: str) -> bool:
        """判断是否为图例内容"""
        if not content:
            return False

        content_lower = content.lower()
        return any(keyword.lower() in content_lower for keyword in self.legend_keywords)

    def _get_texts_bounding_box(self, texts: List[Dict]) -> List[float]:
        """获取文本的边界框"""
        if not texts:
            return [0, 0, 0, 0]

        coords = [t["插入点"] for t in texts if t.get("插入点")]
        if not coords:
            return [0, 0, 0, 0]

        x_coords = [c[0] for c in coords]
        y_coords = [c[1] for c in coords]

        return [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]

    def _validate_sheet_content(self, sheet: Dict):
        """验证图纸内容的完整性"""
        sheet_name = sheet["图纸名称"]
        tables = sheet["表格列表"]
        legends = sheet["图例列表"]

        # 检查是否有图例
        has_legend = len(legends) > 0

        # 检查是否有工程表格
        has_engineering_table = any(
            table["表格类型"] == "工程表格" for table in tables
        )

        # 记录缺失的内容
        missing_content = []

        if not has_legend:
            missing_content.append("图例")

        if not has_engineering_table:
            missing_content.append("工程表格")

        if missing_content:
            logger.warning(f"⚠️  {sheet_name} 缺少以下内容: {', '.join(missing_content)}")
            sheet["缺失内容"] = missing_content
        else:
            logger.info(f"✓ {sheet_name} 内容完整")
            sheet["内容完整"] = True

    def visualize_detection_results(self, save_path: Optional[str] = None):
        """可视化检测结果"""
        if not self.all_rectangles:
            logger.warning("没有检测到矩形，无法可视化")
            return

        fig, ax = plt.subplots(1, 1, figsize=(20, 15))

        # 绘制所有线段
        for line in self.line_entities:
            start = line["起点"]
            end = line["终点"]
            ax.plot([start[0], end[0]], [start[1], end[1]], 'lightgray', alpha=0.3, linewidth=0.5)

        # 绘制主框架
        for i, frame in enumerate(self.main_frames):
            rect = plt.Rectangle(
                (frame.x_min, frame.y_min), frame.width, frame.height,
                fill=False, edgecolor='red', linewidth=3, linestyle='-'
            )
            ax.add_patch(rect)
            ax.text(frame.center_x, frame.center_y, f'主框架{i + 1}',
                    ha='center', va='center', fontsize=12, color='red', weight='bold')

        # 绘制表格框
        for i, frame in enumerate(self.table_frames[:10]):  # 只显示前10个
            rect = plt.Rectangle(
                (frame.x_min, frame.y_min), frame.width, frame.height,
                fill=False, edgecolor='blue', linewidth=2, linestyle='--'
            )
            ax.add_patch(rect)
            ax.text(frame.center_x, frame.center_y, f'表格{i + 1}',
                    ha='center', va='center', fontsize=8, color='blue')

        # 绘制文本点
        for text in self.text_entities:
            coords = text.get("插入点")
            if coords and len(coords) >= 2:
                ax.plot(coords[0], coords[1], 'ro', markersize=1, alpha=0.5)

        ax.set_xlabel('X坐标')
        ax.set_ylabel('Y坐标')
        ax.set_title(f'DXF矩形检测结果 - {Path(self.dxf_path).name}')
        ax.grid(True, alpha=0.3)
        ax.axis('equal')

        # 添加图例
        from matplotlib.lines import Line2D
        legend_elements = [
            Line2D([0], [0], color='red', linewidth=3, label='主框架'),
            Line2D([0], [0], color='blue', linewidth=2, linestyle='--', label='表格框'),
            Line2D([0], [0], marker='o', color='red', linewidth=0, markersize=3, label='文本点')
        ]
        ax.legend(handles=legend_elements, loc='upper right')

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"可视化结果已保存到: {save_path}")

        plt.show()

    def crop_and_save_drawing_sheets(self, output_dir: Optional[str] = None) -> Dict:
        """裁剪并保存所有检测到的图纸区域"""
        if not self.drawing_sheets:
            logger.warning("没有检测到图纸区域，无法进行裁剪")
            return {"成功": False, "错误": "没有检测到图纸区域"}

        # 创建输出目录
        if output_dir is None:
            output_dir = Path(self.dxf_path).parent / "cropped_sheets"
        else:
            output_dir = Path(output_dir)

        output_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"开始裁剪图纸区域，输出目录: {output_dir}")

        # 首先生成完整的DXF图像
        full_image_path = self._generate_full_dxf_image(output_dir)
        if not full_image_path:
            logger.error("无法生成完整的DXF图像")
            return {"成功": False, "错误": "无法生成完整的DXF图像"}

        # 裁剪每个图纸区域
        cropped_files = []
        for i, sheet in enumerate(self.drawing_sheets):
            try:
                cropped_file = self._crop_sheet_region(full_image_path, sheet, output_dir, i + 1)
                if cropped_file:
                    cropped_files.append(cropped_file)
                    logger.info(f"✅ 成功裁剪 {sheet['图纸名称']}: {cropped_file}")
                else:
                    logger.warning(f"⚠️ 裁剪失败: {sheet['图纸名称']}")
            except Exception as e:
                logger.error(f"❌ 裁剪 {sheet['图纸名称']} 时出错: {e}")

        # 生成裁剪报告
        report = {
            "成功": True,
            "输出目录": str(output_dir),
            "完整图像": str(full_image_path),
            "裁剪文件数量": len(cropped_files),
            "裁剪文件列表": cropped_files,
            "图纸详情": []
        }

        # 添加每个图纸的详细信息
        for i, sheet in enumerate(self.drawing_sheets):
            sheet_info = {
                "图纸名称": sheet["图纸名称"],
                "图纸索引": i + 1,
                "边界坐标": {
                    "x_min": sheet["主框架"].x_min,
                    "y_min": sheet["主框架"].y_min,
                    "x_max": sheet["主框架"].x_max,
                    "y_max": sheet["主框架"].y_max
                },
                "尺寸": {
                    "宽度": sheet["主框架"].width,
                    "高度": sheet["主框架"].height
                },
                "裁剪文件": cropped_files[i] if i < len(cropped_files) else None
            }
            report["图纸详情"].append(sheet_info)

        # 保存裁剪报告
        report_path = output_dir / "cropping_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)

        logger.info(f"✅ 图纸裁剪完成，共生成 {len(cropped_files)} 个裁剪文件")
        logger.info(f"📄 裁剪报告已保存: {report_path}")

        return report

    def _generate_full_dxf_image(self, output_dir: Path) -> Optional[str]:
        """生成完整的DXF图像"""
        try:
            logger.info("生成完整的DXF图像...")

            # 创建高分辨率的图像
            fig, ax = plt.subplots(1, 1, figsize=(30, 20))
            ax.set_facecolor('white')

            # 绘制所有线段
            for line in self.line_entities:
                start = line["起点"]
                end = line["终点"]
                ax.plot([start[0], end[0]], [start[1], end[1]], 'black', linewidth=0.8, alpha=0.8)

            # 绘制文本点（可选，用于调试）
            # for text in self.text_entities:
            #     coords = text.get("插入点")
            #     if coords and len(coords) >= 2:
            #         ax.plot(coords[0], coords[1], 'ro', markersize=0.5, alpha=0.3)

            # 设置坐标轴
            ax.set_aspect('equal')
            ax.axis('off')  # 隐藏坐标轴

            # 计算图像边界
            all_x = []
            all_y = []
            for line in self.line_entities:
                all_x.extend([line["起点"][0], line["终点"][0]])
                all_y.extend([line["起点"][1], line["终点"][1]])

            if all_x and all_y:
                margin = max(max(all_x) - min(all_x), max(all_y) - min(all_y)) * 0.05
                ax.set_xlim(min(all_x) - margin, max(all_x) + margin)
                ax.set_ylim(min(all_y) - margin, max(all_y) + margin)

            # 保存高分辨率图像
            full_image_path = output_dir / f"{Path(self.dxf_path).stem}_full_drawing.png"
            plt.savefig(full_image_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none', pad_inches=0.1)
            plt.close()

            logger.info(f"✅ 完整图像已生成: {full_image_path}")
            return str(full_image_path)

        except Exception as e:
            logger.error(f"生成完整DXF图像失败: {e}")
            return None

    def _crop_sheet_region(self, full_image_path: str, sheet: Dict, output_dir: Path, sheet_index: int) -> Optional[str]:
        """裁剪单个图纸区域"""
        try:
            # 打开完整图像
            full_image = Image.open(full_image_path)
            img_width, img_height = full_image.size

            # 获取图纸边界
            frame = sheet["主框架"]

            # 计算所有坐标的范围（用于坐标转换）
            all_x = []
            all_y = []
            for line in self.line_entities:
                all_x.extend([line["起点"][0], line["终点"][0]])
                all_y.extend([line["起点"][1], line["终点"][1]])

            if not all_x or not all_y:
                return None

            # DXF坐标范围
            dxf_x_min, dxf_x_max = min(all_x), max(all_x)
            dxf_y_min, dxf_y_max = min(all_y), max(all_y)
            dxf_width = dxf_x_max - dxf_x_min
            dxf_height = dxf_y_max - dxf_y_min

            # 坐标转换：DXF坐标 -> 图像像素坐标
            def dxf_to_pixel(x, y):
                # 考虑边距
                margin_ratio = 0.05
                effective_width = img_width * (1 - 2 * margin_ratio)
                effective_height = img_height * (1 - 2 * margin_ratio)

                pixel_x = (x - dxf_x_min) / dxf_width * effective_width + img_width * margin_ratio
                # Y坐标需要翻转（图像坐标系Y轴向下，DXF坐标系Y轴向上）
                pixel_y = img_height - ((y - dxf_y_min) / dxf_height * effective_height + img_height * margin_ratio)

                return int(pixel_x), int(pixel_y)

            # 转换图纸边界坐标
            x1, y1 = dxf_to_pixel(frame.x_min, frame.y_max)  # 左上角
            x2, y2 = dxf_to_pixel(frame.x_max, frame.y_min)  # 右下角

            # 确保坐标在图像范围内
            x1 = max(0, min(x1, img_width))
            y1 = max(0, min(y1, img_height))
            x2 = max(0, min(x2, img_width))
            y2 = max(0, min(y2, img_height))

            # 确保有效的裁剪区域
            if x2 <= x1 or y2 <= y1:
                logger.warning(f"无效的裁剪区域: ({x1}, {y1}) -> ({x2}, {y2})")
                return None

            # 添加一些边距
            margin = 20
            x1 = max(0, x1 - margin)
            y1 = max(0, y1 - margin)
            x2 = min(img_width, x2 + margin)
            y2 = min(img_height, y2 + margin)

            # 裁剪图像
            cropped_image = full_image.crop((x1, y1, x2, y2))

            # 生成输出文件名
            sheet_name = sheet["图纸名称"].replace("/", "_").replace("\\", "_")
            output_filename = f"{Path(self.dxf_path).stem}_{sheet_name}_{sheet_index:02d}.png"
            output_path = output_dir / output_filename

            # 保存裁剪的图像
            cropped_image.save(output_path, 'PNG', quality=95)

            logger.info(f"裁剪区域: DXF({frame.x_min:.0f}, {frame.y_min:.0f}) -> ({frame.x_max:.0f}, {frame.y_max:.0f})")
            logger.info(f"像素区域: ({x1}, {y1}) -> ({x2}, {y2}), 尺寸: {x2-x1}x{y2-y1}")

            return str(output_path)

        except Exception as e:
            logger.error(f"裁剪图纸区域失败: {e}")
            return None

    def generate_comprehensive_report(self) -> Dict:
        """生成全面的解析报告"""
        report = {
            "文件信息": {
                "文件名": Path(self.dxf_path).name,
                "文件路径": self.dxf_path,
                "DXF版本": self.doc.dxfversion if self.doc else "未知"
            },
            "实体统计": {
                "总实体数": len(self.all_entities),
                "线段数": len(self.line_entities),
                "文本数": len(self.text_entities)
            },
            "矩形检测结果": {
                "总矩形数": len(self.all_rectangles),
                "主框架数": len(self.main_frames),
                "表格框数": len(self.table_frames)
            },
            "图纸结构": {
                "图纸数量": len(self.drawing_sheets),
                "图纸详情": []
            }
        }

        # 添加图纸详情
        for sheet in self.drawing_sheets:
            sheet_info = {
                "图纸名称": sheet["图纸名称"],
                "图纸类型": sheet["图纸类型"],
                "X范围": sheet["X范围"],
                "Y范围": sheet["Y范围"],
                "面积": sheet["面积"],
                "尺寸": sheet["尺寸"],
                "表格数量": sheet.get("表格数量", 0),
                "图例数量": sheet.get("图例数量", 0),
                "主图文本数量": sheet.get("主图文本数量", 0),
                "内容完整": sheet.get("内容完整", False),
                "缺失内容": sheet.get("缺失内容", [])
            }

            # 添加表格详情
            if sheet.get("表格列表"):
                sheet_info["表格详情"] = []
                for table in sheet["表格列表"]:
                    table_info = {
                        "表格名称": table["表格名称"],
                        "表格类型": table["表格类型"],
                        "文本数量": table["文本数量"],
                        "边界": table["边界"]
                    }
                    sheet_info["表格详情"].append(table_info)

            # 添加图例详情
            if sheet.get("图例列表"):
                sheet_info["图例详情"] = []
                for legend in sheet["图例列表"]:
                    legend_info = {
                        "图例名称": legend["表格名称"],
                        "文本数量": legend["文本数量"],
                        "边界": legend["边界"]
                    }
                    sheet_info["图例详情"].append(legend_info)

            report["图纸结构"]["图纸详情"].append(sheet_info)

        return report

    def generate_markdown_output(self) -> str:
        """生成markdown格式的输出"""
        lines = []

        lines.append("# DXF图纸解析结果（基于外框线检测）")
        lines.append("")
        lines.append(f"**文件名**: {Path(self.dxf_path).name}")
        lines.append(f"**解析时间**: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")

        # 检测结果摘要
        lines.append("## 检测结果摘要")
        lines.append("")
        lines.append(f"- **总实体数**: {len(self.all_entities)}")
        lines.append(f"- **线段数**: {len(self.line_entities)}")
        lines.append(f"- **文本数**: {len(self.text_entities)}")
        lines.append(f"- **检测到矩形**: {len(self.all_rectangles)}")
        lines.append(f"- **主框架**: {len(self.main_frames)}")
        lines.append(f"- **表格框**: {len(self.table_frames)}")
        lines.append(f"- **图纸数量**: {len(self.drawing_sheets)}")
        lines.append("")

        # 图纸详情
        for sheet in self.drawing_sheets:
            lines.append(f"## {sheet['图纸名称']}")
            lines.append("")
            lines.append(f"**基本信息**:")
            lines.append(f"- 图纸类型: {sheet['图纸类型']}")
            lines.append(f"- X范围: {sheet['X范围'][0]:.0f} ~ {sheet['X范围'][1]:.0f}")
            lines.append(f"- Y范围: {sheet['Y范围'][0]:.0f} ~ {sheet['Y范围'][1]:.0f}")
            lines.append(f"- 面积: {sheet['面积']:.0f}")
            lines.append(f"- 尺寸: {sheet['尺寸'][0]:.0f} × {sheet['尺寸'][1]:.0f}")
            lines.append("")

            # 内容统计
            lines.append(f"**内容统计**:")
            lines.append(f"- 表格数量: {sheet.get('表格数量', 0)}")
            lines.append(f"- 图例数量: {sheet.get('图例数量', 0)}")
            lines.append(f"- 主图文本数量: {sheet.get('主图文本数量', 0)}")

            if sheet.get("缺失内容"):
                lines.append(f"- ⚠️ 缺失内容: {', '.join(sheet['缺失内容'])}")
            elif sheet.get("内容完整"):
                lines.append(f"- ✅ 内容完整")
            lines.append("")

            # 表格详情
            if sheet.get("表格列表"):
                lines.append("### 表格详情")
                lines.append("")
                for table in sheet["表格列表"]:
                    lines.append(f"#### {table['表格名称']} - {table['表格类型']}")
                    lines.append("")
                    lines.append(f"- 文本数量: {table['文本数量']}")
                    lines.append(f"- 边界: {table['边界']}")

                    # 表格内容预览
                    if table.get("文本内容"):
                        lines.append("")
                        lines.append("**内容预览**:")
                        for i, text in enumerate(table["文本内容"][:5]):  # 只显示前5个
                            content = text.get("文本内容", "")[:100]  # 限制长度
                            lines.append(f"- {content}")
                        if len(table["文本内容"]) > 5:
                            lines.append(f"- ... (还有{len(table['文本内容']) - 5}个文本)")
                    lines.append("")

            # 图例详情
            if sheet.get("图例列表"):
                lines.append("### 图例详情")
                lines.append("")
                for legend in sheet["图例列表"]:
                    lines.append(f"#### {legend['表格名称']}")
                    lines.append("")
                    lines.append(f"- 文本数量: {legend['文本数量']}")
                    lines.append(f"- 边界: {legend['边界']}")

                    # 图例内容预览
                    if legend.get("文本内容"):
                        lines.append("")
                        lines.append("**图例内容**:")
                        for text in legend["文本内容"][:10]:  # 显示前10个
                            content = text.get("文本内容", "")
                            if content:
                                lines.append(f"- {content}")
                    lines.append("")

            lines.append("---")
            lines.append("")

        return "\n".join(lines)

    def parse_comprehensive(self) -> Dict:
        """执行完整的解析流程"""
        logger.info("开始基于外框线的DXF解析...")

        # 1. 加载文档
        if not self.load_document():
            return {"错误": "无法加载DXF文件"}

        # 2. 提取实体
        logger.info("步骤1: 提取所有实体...")
        self.extract_all_entities()

        # 3. 检测矩形外框
        logger.info("步骤2: 检测矩形外框...")
        self.detect_rectangles()

        # 4. 创建图纸区域
        logger.info("步骤3: 创建图纸区域...")
        self.create_drawing_sheets_from_frames()

        # 5. 检测表格和图例
        logger.info("步骤4: 检测表格和图例...")
        self.detect_tables_and_legends_in_sheets()

        # 6. 裁剪并保存图纸区域
        logger.info("步骤5: 裁剪并保存图纸区域...")
        cropping_result = self.crop_and_save_drawing_sheets()

        # 7. 生成报告
        logger.info("步骤6: 生成解析报告...")
        report = self.generate_comprehensive_report()
        markdown_output = self.generate_markdown_output()

        result = {
            "解析报告": report,
            "markdown输出": markdown_output,
            "裁剪结果": cropping_result,
            "解析成功": True
        }

        logger.info("✅ DXF解析完成")
        return result


def parse_dxf_with_rectangles(dxf_path: str, output_dir: Optional[str] = None) -> Dict:
    """使用基于外框线的方法解析DXF文件"""
    parser = DXFRectangleBasedParser(dxf_path)

    # 执行解析
    result = parser.parse_comprehensive()

    if result.get("解析成功"):
        # 创建输出目录
        if output_dir is None:
            output_dir = Path(dxf_path).parent / "rectangle_based_analysis"
        else:
            output_dir = Path(output_dir)

        output_dir.mkdir(exist_ok=True)

        # 保存JSON报告
        json_path = output_dir / f"{Path(dxf_path).stem}_rectangle_analysis.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(result["解析报告"], f, ensure_ascii=False, indent=2, default=str)

        # 保存Markdown报告
        md_path = output_dir / f"{Path(dxf_path).stem}_rectangle_analysis.md"
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write(result["markdown输出"])

        # 生成可视化图
        viz_path = output_dir / f"{Path(dxf_path).stem}_rectangle_detection.png"
        parser.visualize_detection_results(str(viz_path))

        # 如果有裁剪结果，将裁剪文件移动到主输出目录
        cropping_result = result.get("裁剪结果", {})
        if cropping_result.get("成功") and cropping_result.get("裁剪文件列表"):
            # 创建裁剪图像子目录
            cropped_dir = output_dir / "cropped_sheets"
            cropped_dir.mkdir(exist_ok=True)

            # 复制裁剪文件到主输出目录
            import shutil
            cropped_files_in_output = []
            for cropped_file in cropping_result["裁剪文件列表"]:
                if os.path.exists(cropped_file):
                    filename = Path(cropped_file).name
                    dest_path = cropped_dir / filename
                    shutil.copy2(cropped_file, dest_path)
                    cropped_files_in_output.append(str(dest_path))

            # 更新裁剪报告
            if cropped_files_in_output:
                cropping_report = cropped_dir / "cropping_report.json"
                cropping_result["裁剪文件列表"] = cropped_files_in_output
                cropping_result["输出目录"] = str(cropped_dir)
                with open(cropping_report, 'w', encoding='utf-8') as f:
                    json.dump(cropping_result, f, ensure_ascii=False, indent=2, default=str)

                logger.info(f"📸 图纸裁剪文件已保存到: {cropped_dir}")
                logger.info(f"📄 裁剪报告: {cropping_report}")

        logger.info(f"解析结果已保存到: {output_dir}")

        return {
            "成功": True,
            "输出目录": str(output_dir),
            "报告": result["解析报告"],
            "裁剪结果": result.get("裁剪结果", {})
        }

    else:
        logger.error("解析失败")
        return {
            "成功": False,
            "错误": result.get("错误", "未知错误")
        }


def batch_parse_dxf_files(input_path: Union[str, Path], output_base: Optional[str] = None) -> Dict:
    """批量解析DXF文件"""
    input_path = Path(input_path)

    if not input_path.exists():
        raise FileNotFoundError(f"输入路径不存在: {input_path}")

    # 获取DXF文件列表
    dxf_files = []
    if input_path.is_file():
        if input_path.suffix.lower() == '.dxf':
            dxf_files.append(input_path)
        else:
            raise ValueError(f"输入文件不是DXF格式: {input_path}")
    elif input_path.is_dir():
        dxf_files = list(input_path.rglob('*.dxf')) + list(input_path.rglob('*.DXF'))
        if not dxf_files:
            raise ValueError(f"在目录 {input_path} 中未找到DXF文件")

    # 创建输出目录
    if output_base:
        output_dir = Path(output_base)
    else:
        if input_path.is_file():
            output_dir = input_path.parent / f"{input_path.stem}_rectangle_parsed"
        else:
            output_dir = input_path.parent / f"{input_path.name}_rectangle_parsed"

    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"找到 {len(dxf_files)} 个DXF文件")
    logger.info(f"输出目录: {output_dir}")

    # 处理统计
    results = {
        "成功": 0,
        "失败": 0,
        "总计": len(dxf_files),
        "失败文件": [],
        "成功文件": []
    }

    # 批量处理
    for dxf_file in dxf_files:
        try:
            logger.info(f"\n处理文件: {dxf_file.name}")

            # 为每个文件创建子目录
            file_output_dir = output_dir / dxf_file.stem

            result = parse_dxf_with_rectangles(str(dxf_file), str(file_output_dir))

            if result["成功"]:
                results["成功"] += 1
                results["成功文件"].append(str(dxf_file))
                logger.info(f"✅ 成功解析: {dxf_file.name}")
            else:
                results["失败"] += 1
                results["失败文件"].append(str(dxf_file))
                logger.error(f"❌ 解析失败: {dxf_file.name}")

        except Exception as e:
            results["失败"] += 1
            results["失败文件"].append(str(dxf_file))
            logger.error(f"❌ 解析异常: {dxf_file.name} - {e}")

    # 打印最终统计
    logger.info(f"\n=== 批量解析完成 ===")
    logger.info(f"总文件数: {results['总计']}")
    logger.info(f"成功解析: {results['成功']}")
    logger.info(f"解析失败: {results['失败']}")

    if results["失败文件"]:
        logger.info(f"\n失败文件列表:")
        for failed_file in results["失败文件"]:
            logger.info(f"  - {failed_file}")

    return results


def main():
    """主函数"""
    import sys

    if len(sys.argv) > 1:
        input_path = sys.argv[1]
        output_base = sys.argv[2] if len(sys.argv) > 2 else None
    else:
        # 默认测试路径
        input_path = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test'
        output_base = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_rectangle'

    try:
        results = batch_parse_dxf_files(input_path, output_base)

        if results["总计"] > 0:
            success_rate = (results["成功"] / results["总计"]) * 100
            logger.info(f"\n解析成功率: {success_rate:.1f}%")

        return 0

    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    exit(main())
