#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版DXF解析器功能演示
展示多分图纸和表格重建的新功能
"""

import os
import sys
import json
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def create_demo_output():
    """创建演示输出，展示新功能的数据结构"""
    print("🎨 增强版DXF解析器功能演示")
    print("=" * 60)
    
    # 模拟增强版解析结果
    demo_result = {
        "文件元数据": {
            "文件名": "multi_sheet_drawing.dxf",
            "DXF版本": "AC1027",
            "图纸数量": 2,
            "解析策略": "空白区域分割",
            "检测到的表格总数": 3
        },
        "图纸结构": [
            {
                "图纸名称": "图纸1",
                "图纸边界": {
                    "x_min": 0, "x_max": 2000,
                    "y_min": 0, "y_max": 1500
                },
                "主图内容": [
                    {"类型": "文本", "内容": "主回路图", "图层": "TEXT"},
                    {"类型": "文本", "内容": "380V配电系统", "图层": "TITLE"}
                ],
                "图例内容": [
                    {"类型": "文本", "内容": "图例", "图层": "LEGEND"},
                    {"类型": "文本", "内容": "断路器符号", "图层": "LEGEND"}
                ],
                "表格列表": [
                    {
                        "表格标题": "工程信息表",
                        "表格类型": "工程表格",
                        "行数": 6,
                        "列数": 3,
                        "合并单元格": [
                            [0, 0, 0, 2],  # 第一行跨3列
                            [2, 1, 3, 1]   # 第2-3行第2列合并
                        ],
                        "数据": [
                            ["项目名称：核电站建设项目", "", ""],
                            ["工程号", "2024-NPP-001", "版本"],
                            ["设计", "张工程师", "A"],
                            ["审核", "李主任", ""],
                            ["日期", "2024-01-15", "比例"],
                            ["", "", "1:100"]
                        ],
                        "网格坐标": {
                            "x_grid": [1500, 1700, 1900, 2000],
                            "y_grid": [1400, 1300, 1200, 1100, 1000, 900, 800]
                        }
                    }
                ]
            },
            {
                "图纸名称": "图纸2", 
                "图纸边界": {
                    "x_min": 2100, "x_max": 4100,
                    "y_min": 0, "y_max": 1500
                },
                "主图内容": [
                    {"类型": "文本", "内容": "控制回路图", "图层": "TEXT"},
                    {"类型": "文本", "内容": "24V控制系统", "图层": "TITLE"}
                ],
                "图例内容": [
                    {"类型": "文本", "内容": "控制元件符号", "图层": "LEGEND"}
                ],
                "表格列表": [
                    {
                        "表格标题": "设备清单",
                        "表格类型": "数据表格",
                        "行数": 4,
                        "列数": 4,
                        "合并单元格": [],
                        "数据": [
                            ["序号", "设备名称", "型号", "数量"],
                            ["1", "断路器", "DZ47-63", "3"],
                            ["2", "接触器", "CJX2-2510", "2"],
                            ["3", "热继电器", "JR36-20", "2"]
                        ],
                        "网格坐标": {
                            "x_grid": [3500, 3600, 3800, 3950, 4050],
                            "y_grid": [1400, 1300, 1200, 1100, 1000]
                        }
                    },
                    {
                        "表格标题": "图例说明",
                        "表格类型": "图例表格", 
                        "行数": 3,
                        "列数": 2,
                        "合并单元格": [],
                        "数据": [
                            ["符号", "说明"],
                            ["—||—", "断路器"],
                            ["—()—", "接触器"]
                        ],
                        "网格坐标": {
                            "x_grid": [2200, 2400, 2600],
                            "y_grid": [500, 400, 300, 200]
                        }
                    }
                ]
            }
        ]
    }
    
    return demo_result

def demonstrate_new_features():
    """演示新功能特点"""
    print("\n🆕 新功能特点演示")
    print("=" * 40)
    
    print("1. 📊 多种图纸检测策略:")
    print("   ✅ 矩形图框检测 - 适用于标准图纸")
    print("   ✅ 空白区域分割 - 适用于多图纸布局")
    print("   ✅ 空间聚类分析 - 适用于复杂布局")
    
    print("\n2. 🔧 增强的表格重建:")
    print("   ✅ 精确网格线检测")
    print("   ✅ 多表格区域分离")
    print("   ✅ 合并单元格识别")
    print("   ✅ 智能表格分类")
    
    print("\n3. 📁 表格导出功能:")
    print("   ✅ Excel格式 (.xlsx)")
    print("   ✅ CSV格式 (.csv)")
    print("   ✅ 批量导出")
    print("   ✅ 保持表格结构")

def demonstrate_usage_examples():
    """演示使用示例"""
    print("\n💻 使用示例")
    print("=" * 30)
    
    print("基础使用:")
    print("```python")
    print("from tmp.dxf_parser_optimized_v4 import DXFStructureParser")
    print("")
    print("# 创建解析器")
    print("parser = DXFStructureParser('drawing.dxf')")
    print("")
    print("# 执行解析")
    print("result = parser.parse()")
    print("")
    print("# 查看结果")
    print("print(f'检测到 {result[\"文件元数据\"][\"图纸数量\"]} 张图纸')")
    print("```")
    
    print("\n批量处理:")
    print("```bash")
    print("# 处理整个目录的DXF文件")
    print("python tmp/dxf_parser_optimized_v4.py")
    print("```")
    
    print("\n表格导出:")
    print("```python")
    print("# 表格会自动导出到 {filename}_tables/ 目录")
    print("# 包含Excel和CSV两种格式")
    print("```")

def show_algorithm_improvements():
    """展示算法改进"""
    print("\n🧠 算法改进详解")
    print("=" * 35)
    
    print("图纸检测算法:")
    print("  旧版本: 仅支持矩形图框检测")
    print("  新版本: 三种策略自动选择最佳结果")
    print("    - 矩形检测: 识别标准图框")
    print("    - 空白分割: 基于实体密度分析")
    print("    - 空间聚类: 机器学习算法")
    
    print("\n表格重建算法:")
    print("  旧版本: 简单网格线匹配")
    print("  新版本: 智能表格区域识别")
    print("    - 网格连接分析")
    print("    - BFS连通区域搜索")
    print("    - 合并单元格检测")
    print("    - 多表格分离处理")

def create_sample_output_files():
    """创建示例输出文件"""
    print("\n📄 创建示例输出文件")
    print("=" * 35)
    
    demo_result = create_demo_output()
    
    # 保存JSON结果
    json_file = "./demo_enhanced_result.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(demo_result, f, ensure_ascii=False, indent=2)
    print(f"✅ JSON结果已保存: {json_file}")
    
    # 创建Markdown报告
    md_file = "./demo_enhanced_report.md"
    md_content = generate_markdown_report(demo_result)
    with open(md_file, 'w', encoding='utf-8') as f:
        f.write(md_content)
    print(f"✅ Markdown报告已保存: {md_file}")
    
    # 创建表格数据示例
    create_sample_table_files()

def generate_markdown_report(result):
    """生成Markdown格式报告"""
    md_lines = [
        f"# DXF解析报告: {result['文件元数据']['文件名']}\n",
        f"**DXF版本**: {result['文件元数据']['DXF版本']}  ",
        f"**图纸数量**: {result['文件元数据']['图纸数量']}  ",
        f"**解析策略**: {result['文件元数据']['解析策略']}  ",
        f"**表格总数**: {result['文件元数据']['检测到的表格总数']}\n"
    ]
    
    for i, sheet in enumerate(result['图纸结构']):
        md_lines.append(f"## {sheet['图纸名称']}\n")
        md_lines.append(f"**图纸边界**: ({sheet['图纸边界']['x_min']}, {sheet['图纸边界']['y_min']}) - ({sheet['图纸边界']['x_max']}, {sheet['图纸边界']['y_max']})\n")
        
        # 表格信息
        for j, table in enumerate(sheet['表格列表']):
            md_lines.append(f"### {table['表格标题']} ({table['表格类型']})\n")
            md_lines.append(f"**尺寸**: {table['行数']} 行 × {table['列数']} 列\n")
            
            if table['合并单元格']:
                md_lines.append(f"**合并单元格**: {len(table['合并单元格'])} 个\n")
            
            # 创建表格
            if table['数据']:
                headers = table['数据'][0]
                md_lines.append("| " + " | ".join(headers) + " |")
                md_lines.append("| " + " | ".join(["---"] * len(headers)) + " |")
                
                for row in table['数据'][1:]:
                    cleaned_row = [str(cell).replace("|", "\\|") for cell in row]
                    md_lines.append("| " + " | ".join(cleaned_row) + " |")
            
            md_lines.append("")
    
    return "\n".join(md_lines)

def create_sample_table_files():
    """创建示例表格文件"""
    print("📊 创建示例表格文件...")
    
    # 创建示例目录
    tables_dir = "./demo_tables"
    os.makedirs(tables_dir, exist_ok=True)
    
    # 示例表格数据
    table_data = [
        ["项目名称：核电站建设项目", "", ""],
        ["工程号", "2024-NPP-001", "版本"],
        ["设计", "张工程师", "A"],
        ["审核", "李主任", ""],
        ["日期", "2024-01-15", "比例"],
        ["", "", "1:100"]
    ]
    
    # 创建CSV文件
    csv_file = os.path.join(tables_dir, "table_01_图纸1_工程信息表.csv")
    with open(csv_file, 'w', encoding='utf-8-sig') as f:
        for row in table_data:
            f.write(",".join(f'"{cell}"' for cell in row) + "\n")
    print(f"✅ CSV示例已创建: {csv_file}")
    
    print(f"📁 表格文件目录: {tables_dir}")

def main():
    """主演示函数"""
    print("🎉 增强版DXF解析器完整功能演示")
    print("=" * 80)
    
    # 1. 展示新功能特点
    demonstrate_new_features()
    
    # 2. 演示使用示例
    demonstrate_usage_examples()
    
    # 3. 展示算法改进
    show_algorithm_improvements()
    
    # 4. 创建示例输出文件
    create_sample_output_files()
    
    print("\n🎯 总结")
    print("=" * 20)
    print("✅ 多分图纸检测: 三种策略自动选择")
    print("✅ 表格重建优化: 智能网格分析")
    print("✅ 合并单元格: 自动识别和处理")
    print("✅ 数据导出: Excel/CSV双格式")
    print("✅ 批量处理: 支持目录级操作")
    
    print("\n🚀 开始使用:")
    print("1. 安装依赖: python install_dependencies.py")
    print("2. 运行测试: python test_enhanced_parser.py")
    print("3. 处理文件: python tmp/dxf_parser_optimized_v4.py")
    
    print("\n" + "=" * 80)
    print("演示完成！增强版解析器已准备就绪。")

if __name__ == '__main__':
    main()
