#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DXF解析器依赖项安装脚本
自动安装所需的Python包
"""

import subprocess
import sys
import os

def install_package(package_name, description=""):
    """安装单个包"""
    try:
        print(f"🔄 正在安装 {package_name}...")
        if description:
            print(f"   用途: {description}")
        
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        
        print(f"✅ {package_name} 安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败:")
        print(f"   错误: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ {package_name} 安装过程中出错: {e}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def install_basic_dependencies():
    """安装基础依赖项"""
    print("📦 安装基础依赖项")
    print("=" * 40)
    
    basic_packages = [
        ("ezdxf", "DXF文件读取和处理"),
        ("numpy", "数值计算和数组操作"),
        ("tqdm", "进度条显示"),
    ]
    
    success_count = 0
    for package, description in basic_packages:
        if check_package(package.split('[')[0]):  # 处理带额外选项的包名
            print(f"✅ {package} 已安装")
            success_count += 1
        else:
            if install_package(package, description):
                success_count += 1
    
    print(f"\n基础依赖项安装完成: {success_count}/{len(basic_packages)}")
    return success_count == len(basic_packages)

def install_optional_dependencies():
    """安装可选依赖项"""
    print("\n📦 安装可选依赖项")
    print("=" * 40)
    
    optional_packages = [
        ("pandas", "表格数据处理和导出"),
        ("scikit-learn", "空间聚类分析"),
        ("openpyxl", "Excel文件导出"),
        ("matplotlib", "图形渲染和可视化"),
    ]
    
    success_count = 0
    for package, description in optional_packages:
        if check_package(package.replace('-', '_')):  # sklearn -> sklearn
            print(f"✅ {package} 已安装")
            success_count += 1
        else:
            if install_package(package, description):
                success_count += 1
    
    print(f"\n可选依赖项安装完成: {success_count}/{len(optional_packages)}")
    return success_count

def install_development_dependencies():
    """安装开发依赖项"""
    print("\n📦 安装开发依赖项")
    print("=" * 40)
    
    dev_packages = [
        ("pytest", "单元测试框架"),
        ("black", "代码格式化工具"),
        ("flake8", "代码质量检查"),
    ]
    
    success_count = 0
    for package, description in dev_packages:
        if check_package(package):
            print(f"✅ {package} 已安装")
            success_count += 1
        else:
            if install_package(package, description):
                success_count += 1
    
    print(f"\n开发依赖项安装完成: {success_count}/{len(dev_packages)}")
    return success_count

def upgrade_pip():
    """升级pip到最新版本"""
    print("🔄 升级pip到最新版本...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], check=True, capture_output=True)
        print("✅ pip升级成功")
        return True
    except subprocess.CalledProcessError:
        print("⚠️  pip升级失败，但不影响后续安装")
        return False

def verify_installation():
    """验证安装结果"""
    print("\n🔍 验证安装结果")
    print("=" * 40)
    
    # 基础依赖验证
    basic_deps = ["ezdxf", "numpy", "tqdm"]
    print("基础依赖:")
    for dep in basic_deps:
        status = "✅" if check_package(dep) else "❌"
        print(f"   {status} {dep}")
    
    # 可选依赖验证
    optional_deps = [
        ("pandas", "pandas"),
        ("scikit-learn", "sklearn"),
        ("openpyxl", "openpyxl"),
        ("matplotlib", "matplotlib")
    ]
    print("\n可选依赖:")
    for display_name, import_name in optional_deps:
        status = "✅" if check_package(import_name) else "❌"
        print(f"   {status} {display_name}")
    
    # 功能可用性总结
    print("\n📋 功能可用性:")
    print(f"   基础DXF解析: {'✅' if check_package('ezdxf') else '❌'}")
    print(f"   表格导出: {'✅' if check_package('pandas') else '❌'}")
    print(f"   Excel导出: {'✅' if check_package('openpyxl') else '❌'}")
    print(f"   空间聚类: {'✅' if check_package('sklearn') else '❌'}")
    print(f"   图形渲染: {'✅' if check_package('matplotlib') else '❌'}")

def main():
    """主安装函数"""
    print("🚀 DXF解析器依赖项安装工具")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        print(f"   当前版本: {sys.version}")
        return
    
    print(f"✅ Python版本: {sys.version}")
    
    # 升级pip
    upgrade_pip()
    
    # 询问用户安装选项
    print("\n📋 安装选项:")
    print("1. 仅安装基础依赖项 (最小安装)")
    print("2. 安装基础 + 可选依赖项 (推荐)")
    print("3. 安装所有依赖项 (包括开发工具)")
    
    try:
        choice = input("\n请选择安装选项 (1-3, 默认为2): ").strip()
        if not choice:
            choice = "2"
        
        choice = int(choice)
        if choice not in [1, 2, 3]:
            raise ValueError("无效选择")
    except (ValueError, KeyboardInterrupt):
        print("使用默认选项: 安装基础 + 可选依赖项")
        choice = 2
    
    # 执行安装
    print(f"\n🔄 开始安装 (选项 {choice})...")
    
    # 基础依赖项
    basic_success = install_basic_dependencies()
    
    if not basic_success:
        print("\n❌ 基础依赖项安装失败，请检查网络连接和权限")
        return
    
    # 可选依赖项
    if choice >= 2:
        install_optional_dependencies()
    
    # 开发依赖项
    if choice >= 3:
        install_development_dependencies()
    
    # 验证安装
    verify_installation()
    
    print("\n🎉 安装完成！")
    print("\n💡 下一步:")
    print("   1. 运行测试: python test_enhanced_parser.py")
    print("   2. 解析DXF文件: python tmp/dxf_parser_optimized_v4.py")
    print("   3. 查看使用说明: 参考代码中的注释和文档")

if __name__ == "__main__":
    main()
