# DXF解析器增强版 - 多分图纸与表格重建

## 🎯 功能概述

基于 `dxf_parser_optimized_v4.py` 的增强版本，专门针对多分图纸解析和表格重建进行了深度优化。

### 🆕 新增功能

#### 1. 多种图纸检测策略
- **策略A**: 矩形图框检测 - 识别标准的图纸边框
- **策略B**: X坐标空白区域分割 - 基于空白区域自动分割图纸
- **策略C**: 空间聚类分析 - 使用机器学习算法识别图纸区域

#### 2. 增强的表格重建算法
- **精确网格检测**: 识别横平竖直的网格线
- **多表格分离**: 自动识别独立的表格区域
- **合并单元格检测**: 智能识别跨行跨列的合并单元格
- **表格分类**: 自动分类为图例表格、工程表格或数据表格

#### 3. 表格数据导出
- **Excel导出**: 支持.xlsx格式，保持表格结构
- **CSV导出**: 支持UTF-8编码的CSV文件
- **批量导出**: 自动为每个表格生成独立文件

## 🛠️ 安装依赖

### 自动安装（推荐）
```bash
cd data_process/cad
python install_dependencies.py
```

### 手动安装
```bash
# 基础依赖（必需）
pip install ezdxf numpy tqdm

# 可选依赖（推荐）
pip install pandas scikit-learn openpyxl matplotlib
```

## 🚀 使用方法

### 1. 基本使用
```python
from tmp.dxf_parser_optimized_v4 import DXFStructureParser

# 创建解析器
parser = DXFStructureParser("your_file.dxf")

# 执行解析
result = parser.parse()

# 查看结果
print(f"检测到 {result['文件元数据']['图纸数量']} 张图纸")
for sheet in result['图纸结构']:
    print(f"图纸: {sheet['图纸名称']}")
    print(f"  表格数量: {len(sheet['表格列表'])}")
```

### 2. 批量处理
```python
# 直接运行主脚本
python tmp/dxf_parser_optimized_v4.py
```

### 3. 表格导出
```python
# 解析后自动导出表格
parser = DXFStructureParser("file.dxf")
result = parser.parse()

# 表格会自动导出到 {filename}_tables/ 目录
```

## 📊 输出格式

### JSON结构
```json
{
  "文件元数据": {
    "文件名": "example.dxf",
    "DXF版本": "AC1027",
    "图纸数量": 2
  },
  "图纸结构": [
    {
      "图纸名称": "图纸1",
      "表格列表": [
        {
          "表格标题": "工程信息表",
          "表格类型": "工程表格",
          "行数": 5,
          "列数": 3,
          "合并单元格": [[0, 0, 0, 2]],
          "数据": [["项目名称", "", ""], ["设计", "张工", ""]],
          "网格坐标": {
            "x_grid": [100, 200, 300, 400],
            "y_grid": [500, 400, 300, 200, 100]
          }
        }
      ]
    }
  ]
}
```

### 导出文件
- `{filename}_structure.json` - 完整解析结果
- `{filename}_report.md` - Markdown格式报告
- `{filename}_tables/` - 表格导出目录
  - `table_01_图纸1_表格1.xlsx` - Excel格式表格
  - `table_01_图纸1_表格1.csv` - CSV格式表格

## 🔧 配置参数

### 关键常量
```python
# 几何容差
GEOMETRY_TOLERANCE = 1.0

# 表格网格合并容差
TABLE_GRID_TOLERANCE = 5.0

# 最小图纸尺寸
MIN_SHEET_DIMENSION = 1000.0

# 空白区域最小宽度
MIN_BLANK_WIDTH = 200.0

# 聚类参数
CLUSTERING_EPS = 100.0
CLUSTERING_MIN_SAMPLES = 5
```

## 🧪 测试功能

### 运行测试套件
```bash
python test_enhanced_parser.py
```

### 测试内容
- ✅ 依赖项检查
- ✅ 图纸检测策略测试
- ✅ 表格重建功能测试
- ✅ 导出功能验证

## 📈 算法详解

### 图纸区域划分算法

#### 策略A: 矩形图框检测
1. 遍历所有多段线(POLYLINE)
2. 检查是否为闭合矩形
3. 验证尺寸是否满足最小图纸要求
4. 按X坐标排序并命名

#### 策略B: 空白区域分割
1. 收集所有实体的X坐标
2. 创建X轴直方图统计实体密度
3. 识别连续的低密度区域作为分割线
4. 根据分割线创建图纸边界

#### 策略C: 空间聚类分析
1. 提取所有实体的坐标点
2. 使用DBSCAN算法进行聚类
3. 为每个聚类创建包围盒
4. 过滤掉过小的聚类区域

### 表格重建算法

#### 网格线检测
1. 筛选图纸范围内的直线
2. 分类为水平线和垂直线
3. 合并相近的坐标值
4. 构建网格坐标系

#### 表格区域识别
1. 创建网格连接矩阵
2. 标记实际存在的网格线连接
3. 使用BFS算法找连通区域
4. 为每个连通区域创建表格对象

#### 文本填充与合并单元格检测
1. 根据坐标将文本分配到单元格
2. 检测跨越多个网格的文本
3. 识别合并单元格模式
4. 优化表格数据结构

## 🎨 使用场景

### 适用的DXF文件类型
- ✅ 工程图纸（包含标题栏、明细表）
- ✅ 建筑平面图（多张图纸布局）
- ✅ 电气原理图（含参数表格）
- ✅ 机械装配图（含零件清单）

### 表格类型识别
- **工程表格**: 包含工程号、设计、审核等关键词
- **图例表格**: 包含图例、符号、说明等关键词
- **数据表格**: 其他结构化数据表格

## 🔍 故障排除

### 常见问题

#### 1. 图纸检测失败
- **原因**: 图纸没有标准边框或实体分布不规律
- **解决**: 调整 `MIN_SHEET_DIMENSION` 参数或使用聚类策略

#### 2. 表格重建不准确
- **原因**: 网格线不完整或存在干扰线条
- **解决**: 调整 `TABLE_GRID_TOLERANCE` 参数

#### 3. 导出功能不可用
- **原因**: 缺少pandas或openpyxl依赖
- **解决**: 运行 `python install_dependencies.py`

### 性能优化建议
- 对于大型文件，建议先使用矩形检测策略
- 空间聚类适用于复杂布局但计算量较大
- 可以通过调整聚类参数优化检测效果

## 📝 更新日志

### v4.1 (当前版本)
- ✅ 新增多种图纸检测策略
- ✅ 增强表格重建算法
- ✅ 添加合并单元格检测
- ✅ 支持表格导出功能
- ✅ 改进网格线分析算法
- ✅ 添加依赖管理工具

### 计划功能
- 🔄 图像辅助的版面识别
- 🔄 更智能的表格分类
- 🔄 支持更多导出格式
- 🔄 GUI界面开发

## 📞 技术支持

如有问题或建议，请：
1. 运行测试套件检查功能状态
2. 查看错误日志和调试信息
3. 调整配置参数尝试解决
4. 提供具体的DXF文件样本以便分析
